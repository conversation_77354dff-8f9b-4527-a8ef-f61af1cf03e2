-- Create notifications table for the cafeteria web app
-- Run this in your Supabase SQL editor

CREATE TABLE IF NOT EXISTS notifications (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  title TEXT NOT NULL,
  message TEXT NOT NULL,
  type TEXT DEFAULT 'info' CHECK (type IN ('info', 'warning', 'error', 'success', 'order', 'inventory', 'review', 'support')),
  is_read BOOLEAN DEFAULT false,
  related_order_id UUID NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable RLS
ALTER TABLE notifications ENABLE ROW LEVEL SECURITY;

-- RLS Policy - users can only see their own notifications
CREATE POLICY "Users can view their own notifications"
  ON notifications FOR SELECT
  USING (auth.uid() = user_id);

CREATE POLICY "Users can update their own notifications"
  ON notifications FOR UPDATE
  USING (auth.uid() = user_id);

-- <PERSON><PERSON> indexes for better performance
CREATE INDEX IF NOT EXISTS idx_notifications_user_id ON notifications(user_id);
CREATE INDEX IF NOT EXISTS idx_notifications_created_at ON notifications(created_at);
CREATE INDEX IF NOT EXISTS idx_notifications_is_read ON notifications(is_read);

-- Add missing columns to cafeterias table for settings
ALTER TABLE cafeterias 
ADD COLUMN IF NOT EXISTS online_ordering BOOLEAN DEFAULT true,
ADD COLUMN IF NOT EXISTS auto_accept_orders BOOLEAN DEFAULT false,
ADD COLUMN IF NOT EXISTS default_preparation_time INTEGER DEFAULT 15,
ADD COLUMN IF NOT EXISTS business_hours JSONB DEFAULT NULL;

-- Insert some sample notifications for testing
INSERT INTO notifications (user_id, title, message, type, is_read) 
SELECT 
  auth.uid(),
  'Welcome to UniEats!',
  'Your cafeteria dashboard is ready to use.',
  'info',
  false
WHERE auth.uid() IS NOT NULL
ON CONFLICT DO NOTHING;
