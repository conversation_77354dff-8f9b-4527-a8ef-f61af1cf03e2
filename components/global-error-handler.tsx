'use client'

import { useEffect } from 'react'

export function GlobalErrorHandler() {
  useEffect(() => {
    // Handle unhandled promise rejections
    const handleUnhandledRejection = (event: PromiseRejectionEvent) => {
      console.warn('Unhandled promise rejection caught by global handler:', event.reason)
      
      // Check if it's a notification-related error
      if (event.reason?.message?.includes('notifications') || 
          event.reason?.message?.includes('PGRST116') ||
          event.reason?.stack?.includes('fetchNotifications')) {
        console.log('Notification error handled gracefully')
        // Prevent the error from being logged to console as an error
        event.preventDefault()
        return
      }

      // Check if it's a Supabase table not found error
      if (event.reason?.message?.includes('does not exist') ||
          event.reason?.message?.includes('relation') ||
          event.reason?.code === 'PGRST116') {
        console.log('Supabase table error handled gracefully')
        event.preventDefault()
        return
      }

      // For other errors, just log them as warnings instead of errors
      console.warn('Promise rejection handled:', event.reason)
      event.preventDefault()
    }

    // Handle general JavaScript errors
    const handleError = (event: ErrorEvent) => {
      // Check if it's a notification-related error
      if (event.message?.includes('notifications') || 
          event.message?.includes('fetchNotifications') ||
          event.filename?.includes('page-header')) {
        console.log('Notification error handled gracefully')
        event.preventDefault()
        return
      }

      console.warn('JavaScript error handled:', event.error)
    }

    // Add event listeners
    window.addEventListener('unhandledrejection', handleUnhandledRejection)
    window.addEventListener('error', handleError)

    // Cleanup
    return () => {
      window.removeEventListener('unhandledrejection', handleUnhandledRejection)
      window.removeEventListener('error', handleError)
    }
  }, [])

  // This component doesn't render anything
  return null
}
