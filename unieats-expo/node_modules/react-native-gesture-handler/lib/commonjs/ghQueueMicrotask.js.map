{"version": 3, "sources": ["ghQueueMicrotask.ts"], "names": ["ghQueueMicrotask", "setImmediate", "bind", "requestAnimationFrame", "queueMicrotask"], "mappings": ";;;;;;AAAA;AACA;AACO,MAAMA,gBAAgB,GAC3B,OAAOC,YAAP,KAAwB,UAAxB,GACIA,YAAY,CAACC,IAAb,CAAkB,IAAlB,CADJ,GAEI,OAAOC,qBAAP,KAAiC,UAAjC,GACEA,qBAAqB,CAACD,IAAtB,CAA2B,IAA3B,CADF,GAEEE,cAAc,CAACF,IAAf,CAAoB,IAApB,CALD", "sourcesContent": ["// We check for typeof requestAnimationFrame because of SSR\n// Functions are bound to null to avoid issues with scope when using Metro inline requires.\nexport const ghQueueMicrotask =\n  typeof setImmediate === 'function'\n    ? setImmediate.bind(null)\n    : typeof requestAnimationFrame === 'function'\n      ? requestAnimationFrame.bind(null)\n      : queueMicrotask.bind(null);\n"]}