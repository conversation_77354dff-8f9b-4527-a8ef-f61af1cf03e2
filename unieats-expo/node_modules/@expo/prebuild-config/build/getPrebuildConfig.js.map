{"version": 3, "file": "getPrebuildConfig.js", "names": ["_config", "data", "require", "_getAutolinkedPackages", "_withDefaultPlugins", "getPrebuildConfigAsync", "projectRoot", "props", "autolinkedModules", "getAutolinkedPackagesAsync", "platforms", "getPrebuildConfig", "bundleIdentifier", "packageName", "exp", "config", "rest", "getConfig", "skipSDKVersionRequirement", "isModdedConfig", "_internal", "withVersionedExpoSDKPlugins", "withLegacyExpoPlugins", "includes", "ios", "withIosExpoPlugins", "android", "package", "withAndroidExpoPlugins"], "sources": ["../src/getPrebuildConfig.ts"], "sourcesContent": ["import { getConfig } from '@expo/config';\nimport { ModPlatform } from '@expo/config-plugins';\n\nimport { getAutolinkedPackagesAsync } from './getAutolinkedPackages';\nimport {\n  withAndroidExpoPlugins,\n  withIosExpoPlugins,\n  withLegacyExpoPlugins,\n  withVersionedExpoSDKPlugins,\n} from './plugins/withDefaultPlugins';\n\nexport async function getPrebuildConfigAsync(\n  projectRoot: string,\n  props: {\n    bundleIdentifier?: string;\n    packageName?: string;\n    platforms: ModPlatform[];\n  }\n): Promise<ReturnType<typeof getConfig>> {\n  const autolinkedModules = await getAutolinkedPackagesAsync(projectRoot, props.platforms);\n\n  return getPrebuildConfig(projectRoot, {\n    ...props,\n    autolinkedModules,\n  });\n}\n\nfunction getPrebuildConfig(\n  projectRoot: string,\n  {\n    platforms,\n    bundleIdentifier,\n    packageName,\n    autolinkedModules,\n  }: {\n    bundleIdentifier?: string;\n    packageName?: string;\n    platforms: ModPlatform[];\n    autolinkedModules?: string[];\n  }\n) {\n  // let config: ExpoConfig;\n  let { exp: config, ...rest } = getConfig(projectRoot, {\n    skipSDKVersionRequirement: true,\n    isModdedConfig: true,\n  });\n\n  if (autolinkedModules) {\n    if (!config._internal) {\n      config._internal = {};\n    }\n    config._internal.autolinkedModules = autolinkedModules;\n  }\n\n  // Add all built-in plugins first because they should take\n  // priority over the unversioned plugins.\n  config = withVersionedExpoSDKPlugins(config);\n  config = withLegacyExpoPlugins(config);\n\n  if (platforms.includes('ios')) {\n    if (!config.ios) config.ios = {};\n    config.ios.bundleIdentifier =\n      bundleIdentifier ?? config.ios.bundleIdentifier ?? `com.placeholder.appid`;\n\n    // Add all built-in plugins\n    config = withIosExpoPlugins(config, {\n      bundleIdentifier: config.ios.bundleIdentifier,\n    });\n  }\n\n  if (platforms.includes('android')) {\n    if (!config.android) config.android = {};\n    config.android.package = packageName ?? config.android.package ?? `com.placeholder.appid`;\n\n    // Add all built-in plugins\n    config = withAndroidExpoPlugins(config, {\n      package: config.android.package,\n      projectRoot,\n    });\n  }\n\n  return { exp: config, ...rest };\n}\n"], "mappings": ";;;;;;AAAA,SAAAA,QAAA;EAAA,MAAAC,IAAA,GAAAC,OAAA;EAAAF,OAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAGA,SAAAE,uBAAA;EAAA,MAAAF,IAAA,GAAAC,OAAA;EAAAC,sBAAA,YAAAA,CAAA;IAAA,OAAAF,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAG,oBAAA;EAAA,MAAAH,IAAA,GAAAC,OAAA;EAAAE,mBAAA,YAAAA,CAAA;IAAA,OAAAH,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAOO,eAAeI,sBAAsBA,CAC1CC,WAAmB,EACnBC,KAIC,EACsC;EACvC,MAAMC,iBAAiB,GAAG,MAAM,IAAAC,mDAA0B,EAACH,WAAW,EAAEC,KAAK,CAACG,SAAS,CAAC;EAExF,OAAOC,iBAAiB,CAACL,WAAW,EAAE;IACpC,GAAGC,KAAK;IACRC;EACF,CAAC,CAAC;AACJ;AAEA,SAASG,iBAAiBA,CACxBL,WAAmB,EACnB;EACEI,SAAS;EACTE,gBAAgB;EAChBC,WAAW;EACXL;AAMF,CAAC,EACD;EACA;EACA,IAAI;IAAEM,GAAG,EAAEC,MAAM;IAAE,GAAGC;EAAK,CAAC,GAAG,IAAAC,mBAAS,EAACX,WAAW,EAAE;IACpDY,yBAAyB,EAAE,IAAI;IAC/BC,cAAc,EAAE;EAClB,CAAC,CAAC;EAEF,IAAIX,iBAAiB,EAAE;IACrB,IAAI,CAACO,MAAM,CAACK,SAAS,EAAE;MACrBL,MAAM,CAACK,SAAS,GAAG,CAAC,CAAC;IACvB;IACAL,MAAM,CAACK,SAAS,CAACZ,iBAAiB,GAAGA,iBAAiB;EACxD;;EAEA;EACA;EACAO,MAAM,GAAG,IAAAM,iDAA2B,EAACN,MAAM,CAAC;EAC5CA,MAAM,GAAG,IAAAO,2CAAqB,EAACP,MAAM,CAAC;EAEtC,IAAIL,SAAS,CAACa,QAAQ,CAAC,KAAK,CAAC,EAAE;IAC7B,IAAI,CAACR,MAAM,CAACS,GAAG,EAAET,MAAM,CAACS,GAAG,GAAG,CAAC,CAAC;IAChCT,MAAM,CAACS,GAAG,CAACZ,gBAAgB,GACzBA,gBAAgB,IAAIG,MAAM,CAACS,GAAG,CAACZ,gBAAgB,IAAI,uBAAuB;;IAE5E;IACAG,MAAM,GAAG,IAAAU,wCAAkB,EAACV,MAAM,EAAE;MAClCH,gBAAgB,EAAEG,MAAM,CAACS,GAAG,CAACZ;IAC/B,CAAC,CAAC;EACJ;EAEA,IAAIF,SAAS,CAACa,QAAQ,CAAC,SAAS,CAAC,EAAE;IACjC,IAAI,CAACR,MAAM,CAACW,OAAO,EAAEX,MAAM,CAACW,OAAO,GAAG,CAAC,CAAC;IACxCX,MAAM,CAACW,OAAO,CAACC,OAAO,GAAGd,WAAW,IAAIE,MAAM,CAACW,OAAO,CAACC,OAAO,IAAI,uBAAuB;;IAEzF;IACAZ,MAAM,GAAG,IAAAa,4CAAsB,EAACb,MAAM,EAAE;MACtCY,OAAO,EAAEZ,MAAM,CAACW,OAAO,CAACC,OAAO;MAC/BrB;IACF,CAAC,CAAC;EACJ;EAEA,OAAO;IAAEQ,GAAG,EAAEC,MAAM;IAAE,GAAGC;EAAK,CAAC;AACjC", "ignoreList": []}