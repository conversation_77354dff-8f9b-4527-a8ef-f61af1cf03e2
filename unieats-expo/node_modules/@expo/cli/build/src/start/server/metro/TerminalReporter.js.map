{"version": 3, "sources": ["../../../../../src/start/server/metro/TerminalReporter.ts"], "sourcesContent": ["// This file represents an abstraction on the metro TerminalReporter.\n// We use this abstraction to safely extend the TerminalReporter for our own custom logging.\nimport chalk from 'chalk';\nimport UpstreamTerminalReporter from 'metro/src/lib/TerminalReporter';\nimport { Terminal } from 'metro-core';\nimport type { WatcherStatus } from 'metro-file-map';\nimport util from 'util';\n\nimport {\n  BundleDetails,\n  BundleProgressUpdate,\n  TerminalReportableEvent,\n  TerminalReporterInterface,\n} from './TerminalReporter.types';\nimport { stripAnsi } from '../../../utils/ansi';\n\nconst debug = require('debug')('expo:metro:logger') as typeof console.log;\n\n/**\n * A standard way to log a warning to the terminal. This should not be called\n * from some arbitrary Metro logic, only from the reporters. Instead of\n * calling this, add a new type of ReportableEvent instead, and implement a\n * proper handler in the reporter(s).\n */\nexport function logWarning(terminal: Terminal, format: string, ...args: any[]): void {\n  const str = util.format(format, ...args);\n  terminal.log('%s: %s', chalk.yellow('warning'), str);\n}\n\n/**\n * Similar to `logWarning`, but for messages that require the user to act.\n */\nexport function logError(terminal: Terminal, format: string, ...args: any[]): void {\n  terminal.log(\n    '%s: %s',\n    chalk.red('error'),\n    // Syntax errors may have colors applied for displaying code frames\n    // in various places outside of where Metro is currently running.\n    // If the current terminal does not support color, we'll strip the colors\n    // here.\n    util.format(chalk.supportsColor ? format : stripAnsi(format), ...args)\n  );\n}\n\nconst XTerminalReporter = UpstreamTerminalReporter as unknown as TerminalReporterInterface;\n\n/** Extended TerminalReporter class but with proper types and extra functionality to avoid using the `_log` method directly in subclasses. */\nexport class TerminalReporter extends XTerminalReporter implements TerminalReporterInterface {\n  /**\n   * A cache of { [buildID]: BundleDetails } which can be used to\n   * add more contextual logs. BundleDetails is currently only sent with `bundle_build_started`\n   * so we need to cache the details in order to print the platform info with other event types.\n   */\n  _bundleDetails: Map<string, BundleDetails> = new Map();\n\n  /** Keep track of how long a bundle takes to complete */\n  _bundleTimers: Map<string, bigint> = new Map();\n\n  /** Keep track of bundle processes that should not be logged. */\n  _hiddenBundleEvents: Set<string> = new Set();\n\n  _log(event: TerminalReportableEvent): void {\n    switch (event.type) {\n      case 'transform_cache_reset':\n        return this.transformCacheReset();\n      case 'dep_graph_loading':\n        return this.dependencyGraphLoading(event.hasReducedPerformance);\n      case 'client_log':\n        if (this.shouldFilterClientLog(event)) {\n          return;\n        }\n        break;\n    }\n    return super._log(event);\n  }\n\n  /** Gives subclasses an easy interface for filtering out logs. Return `true` to skip. */\n  shouldFilterClientLog(event: TerminalReportableEvent): boolean {\n    return false;\n  }\n\n  /** Gives subclasses an easy interface for filtering out bundle events, specifically for source maps. Return `true` to skip. */\n  shouldFilterBundleEvent(event: TerminalReportableEvent): boolean {\n    return false;\n  }\n\n  /** Cache has been reset. */\n  transformCacheReset(): void {}\n\n  /** One of the first logs that will be printed. */\n  dependencyGraphLoading(hasReducedPerformance: boolean): void {}\n\n  /**\n   * Custom log event representing the end of the bundling.\n   *\n   * @param event event object.\n   * @param duration duration of the build in milliseconds.\n   */\n  bundleBuildEnded(event: TerminalReportableEvent, duration: bigint | number): void {}\n\n  // Add a custom format to logs that come from the worker threads.\n  // `| <contents>`\n  _logWorkerChunk(origin: 'stdout' | 'stderr', chunk: string): void {\n    const lines = chunk.split('\\n');\n    if (lines.length >= 1 && lines[lines.length - 1] === '') {\n      lines.splice(lines.length - 1, 1);\n    }\n\n    const originTag = origin === 'stdout' ? chalk.dim('|') : chalk.yellow('|');\n    lines.forEach((line: string) => {\n      this.terminal.log(originTag, line);\n    });\n  }\n\n  _logWatcherStatus(status: WatcherStatus) {\n    // Metro logs this warning twice. This helps reduce the noise.\n\n    if (status.type === 'watchman_warning') {\n      return;\n    }\n    return super._logWatcherStatus(status);\n  }\n\n  /**\n   * This function is exclusively concerned with updating the internal state.\n   * No logging or status updates should be done at this point.\n   */\n  _updateState(\n    event: TerminalReportableEvent & { bundleDetails?: BundleDetails; buildID?: string }\n  ) {\n    // Append the buildID to the bundleDetails.\n    if (event.bundleDetails) {\n      event.bundleDetails.buildID = event.buildID;\n    }\n\n    const buildID = event.bundleDetails?.buildID ?? event.buildID;\n\n    if (buildID && !this._hiddenBundleEvents.has(buildID)) {\n      if (this.shouldFilterBundleEvent(event)) {\n        debug('skipping bundle events for', buildID, event);\n        this._hiddenBundleEvents.add(buildID);\n      } else {\n        super._updateState(event);\n      }\n    } else {\n      super._updateState(event);\n    }\n\n    switch (event.type) {\n      case 'bundle_build_done':\n      case 'bundle_build_failed': {\n        const startTime = this._bundleTimers.get(event.buildID);\n        // Observed a bug in Metro where the `bundle_build_done` is invoked twice during a static bundle\n        // i.e. `expo export`.\n        if (startTime == null) {\n          break;\n        }\n\n        this.bundleBuildEnded(event, startTime ? process.hrtime.bigint() - startTime : 0);\n        this._bundleTimers.delete(event.buildID);\n        break;\n      }\n      case 'bundle_build_started':\n        this._bundleDetails.set(event.buildID, event.bundleDetails);\n        this._bundleTimers.set(event.buildID, process.hrtime.bigint());\n        break;\n    }\n  }\n\n  /**\n   * We use Math.pow(ratio, 2) to as a conservative measure of progress because\n   * we know the `totalCount` is going to progressively increase as well. We\n   * also prevent the ratio from going backwards.\n   */\n  _updateBundleProgress(options: BundleProgressUpdate) {\n    super._updateBundleProgress(options);\n\n    const currentProgress = this._activeBundles.get(options.buildID);\n    if (!currentProgress) {\n      return;\n    }\n\n    // Fix an issue where the transformer is faster than the resolver,\n    // locking the progress bar at 100% after transforming the first and only resolved file (1/1).\n    if (currentProgress.ratio === 1 && options.totalFileCount === 1) {\n      Object.assign(currentProgress, { ...currentProgress, ratio: 0 });\n    }\n  }\n}\n"], "names": ["TerminalReporter", "logError", "logWarning", "debug", "require", "terminal", "format", "args", "str", "util", "log", "chalk", "yellow", "red", "supportsColor", "stripAnsi", "XTerminalReporter", "UpstreamTerminalReporter", "_log", "event", "type", "transformCacheReset", "dependencyGraphLoading", "hasReducedPerformance", "shouldFilterClientLog", "shouldFilterBundleEvent", "bundleBuildEnded", "duration", "_logWorkerChunk", "origin", "chunk", "lines", "split", "length", "splice", "originTag", "dim", "for<PERSON>ach", "line", "_logWatcherStatus", "status", "_updateState", "bundleDetails", "buildID", "_hiddenBundleEvents", "has", "add", "startTime", "_bundleTimers", "get", "process", "hrtime", "bigint", "delete", "_bundleDetails", "set", "_updateBundleProgress", "options", "currentProgress", "_activeBundles", "ratio", "totalFileCount", "Object", "assign", "Map", "Set"], "mappings": "AAAA,qEAAqE;AACrE,4FAA4F;;;;;;;;;;;;IA8C/EA,gBAAgB;eAAhBA;;IAfGC,QAAQ;eAARA;;IARAC,UAAU;eAAVA;;;;gEAtBE;;;;;;;gEACmB;;;;;;;gEAGpB;;;;;;sBAQS;;;;;;AAE1B,MAAMC,QAAQC,QAAQ,SAAS;AAQxB,SAASF,WAAWG,QAAkB,EAAEC,MAAc,EAAE,GAAGC,IAAW;IAC3E,MAAMC,MAAMC,eAAI,CAACH,MAAM,CAACA,WAAWC;IACnCF,SAASK,GAAG,CAAC,UAAUC,gBAAK,CAACC,MAAM,CAAC,YAAYJ;AAClD;AAKO,SAASP,SAASI,QAAkB,EAAEC,MAAc,EAAE,GAAGC,IAAW;IACzEF,SAASK,GAAG,CACV,UACAC,gBAAK,CAACE,GAAG,CAAC,UACV,mEAAmE;IACnE,iEAAiE;IACjE,yEAAyE;IACzE,QAAQ;IACRJ,eAAI,CAACH,MAAM,CAACK,gBAAK,CAACG,aAAa,GAAGR,SAASS,IAAAA,eAAS,EAACT,YAAYC;AAErE;AAEA,MAAMS,oBAAoBC,2BAAwB;AAG3C,MAAMjB,yBAAyBgB;IAcpCE,KAAKC,KAA8B,EAAQ;QACzC,OAAQA,MAAMC,IAAI;YAChB,KAAK;gBACH,OAAO,IAAI,CAACC,mBAAmB;YACjC,KAAK;gBACH,OAAO,IAAI,CAACC,sBAAsB,CAACH,MAAMI,qBAAqB;YAChE,KAAK;gBACH,IAAI,IAAI,CAACC,qBAAqB,CAACL,QAAQ;oBACrC;gBACF;gBACA;QACJ;QACA,OAAO,KAAK,CAACD,KAAKC;IACpB;IAEA,sFAAsF,GACtFK,sBAAsBL,KAA8B,EAAW;QAC7D,OAAO;IACT;IAEA,6HAA6H,GAC7HM,wBAAwBN,KAA8B,EAAW;QAC/D,OAAO;IACT;IAEA,0BAA0B,GAC1BE,sBAA4B,CAAC;IAE7B,gDAAgD,GAChDC,uBAAuBC,qBAA8B,EAAQ,CAAC;IAE9D;;;;;GAKC,GACDG,iBAAiBP,KAA8B,EAAEQ,QAAyB,EAAQ,CAAC;IAEnF,iEAAiE;IACjE,iBAAiB;IACjBC,gBAAgBC,MAA2B,EAAEC,KAAa,EAAQ;QAChE,MAAMC,QAAQD,MAAME,KAAK,CAAC;QAC1B,IAAID,MAAME,MAAM,IAAI,KAAKF,KAAK,CAACA,MAAME,MAAM,GAAG,EAAE,KAAK,IAAI;YACvDF,MAAMG,MAAM,CAACH,MAAME,MAAM,GAAG,GAAG;QACjC;QAEA,MAAME,YAAYN,WAAW,WAAWlB,gBAAK,CAACyB,GAAG,CAAC,OAAOzB,gBAAK,CAACC,MAAM,CAAC;QACtEmB,MAAMM,OAAO,CAAC,CAACC;YACb,IAAI,CAACjC,QAAQ,CAACK,GAAG,CAACyB,WAAWG;QAC/B;IACF;IAEAC,kBAAkBC,MAAqB,EAAE;QACvC,8DAA8D;QAE9D,IAAIA,OAAOpB,IAAI,KAAK,oBAAoB;YACtC;QACF;QACA,OAAO,KAAK,CAACmB,kBAAkBC;IACjC;IAEA;;;GAGC,GACDC,aACEtB,KAAoF,EACpF;YAMgBA;QALhB,2CAA2C;QAC3C,IAAIA,MAAMuB,aAAa,EAAE;YACvBvB,MAAMuB,aAAa,CAACC,OAAO,GAAGxB,MAAMwB,OAAO;QAC7C;QAEA,MAAMA,UAAUxB,EAAAA,uBAAAA,MAAMuB,aAAa,qBAAnBvB,qBAAqBwB,OAAO,KAAIxB,MAAMwB,OAAO;QAE7D,IAAIA,WAAW,CAAC,IAAI,CAACC,mBAAmB,CAACC,GAAG,CAACF,UAAU;YACrD,IAAI,IAAI,CAAClB,uBAAuB,CAACN,QAAQ;gBACvChB,MAAM,8BAA8BwC,SAASxB;gBAC7C,IAAI,CAACyB,mBAAmB,CAACE,GAAG,CAACH;YAC/B,OAAO;gBACL,KAAK,CAACF,aAAatB;YACrB;QACF,OAAO;YACL,KAAK,CAACsB,aAAatB;QACrB;QAEA,OAAQA,MAAMC,IAAI;YAChB,KAAK;YACL,KAAK;gBAAuB;oBAC1B,MAAM2B,YAAY,IAAI,CAACC,aAAa,CAACC,GAAG,CAAC9B,MAAMwB,OAAO;oBACtD,gGAAgG;oBAChG,sBAAsB;oBACtB,IAAII,aAAa,MAAM;wBACrB;oBACF;oBAEA,IAAI,CAACrB,gBAAgB,CAACP,OAAO4B,YAAYG,QAAQC,MAAM,CAACC,MAAM,KAAKL,YAAY;oBAC/E,IAAI,CAACC,aAAa,CAACK,MAAM,CAAClC,MAAMwB,OAAO;oBACvC;gBACF;YACA,KAAK;gBACH,IAAI,CAACW,cAAc,CAACC,GAAG,CAACpC,MAAMwB,OAAO,EAAExB,MAAMuB,aAAa;gBAC1D,IAAI,CAACM,aAAa,CAACO,GAAG,CAACpC,MAAMwB,OAAO,EAAEO,QAAQC,MAAM,CAACC,MAAM;gBAC3D;QACJ;IACF;IAEA;;;;GAIC,GACDI,sBAAsBC,OAA6B,EAAE;QACnD,KAAK,CAACD,sBAAsBC;QAE5B,MAAMC,kBAAkB,IAAI,CAACC,cAAc,CAACV,GAAG,CAACQ,QAAQd,OAAO;QAC/D,IAAI,CAACe,iBAAiB;YACpB;QACF;QAEA,kEAAkE;QAClE,8FAA8F;QAC9F,IAAIA,gBAAgBE,KAAK,KAAK,KAAKH,QAAQI,cAAc,KAAK,GAAG;YAC/DC,OAAOC,MAAM,CAACL,iBAAiB;gBAAE,GAAGA,eAAe;gBAAEE,OAAO;YAAE;QAChE;IACF;;QA5IK,gBACL;;;;GAIC,QACDN,iBAA6C,IAAIU,OAEjD,sDAAsD,QACtDhB,gBAAqC,IAAIgB,OAEzC,8DAA8D,QAC9DpB,sBAAmC,IAAIqB;;AAiIzC"}