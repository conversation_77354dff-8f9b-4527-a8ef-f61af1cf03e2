{"version": 3, "sources": ["../../../../../../src/start/server/middleware/inspector/createJsInspectorMiddleware.ts"], "sourcesContent": ["import chalk from 'chalk';\nimport type { NextHandleFunction } from 'connect';\nimport type { IncomingMessage, ServerResponse } from 'http';\nimport net from 'net';\nimport { TLSSocket } from 'tls';\nimport { URL } from 'url';\n\nimport { openJsInspector, queryInspectorAppAsync } from './JsInspector';\n\n/**\n * Create a middleware that handles new requests to open the debugger from the dev menu.\n * @todo(cedric): delete this middleware once we fully swap over to the new React Native JS Inspector.\n */\nexport function createJsInspectorMiddleware(): NextHandleFunction {\n  return async function (req: IncomingMessage, res: ServerResponse, next: (err?: Error) => void) {\n    const { origin, searchParams } = new URL(req.url ?? '/', getServerBase(req));\n    const appId = searchParams.get('appId') || searchParams.get('applicationId');\n    if (!appId) {\n      res.writeHead(400).end('Missing application identifier (\"?appId=...\")');\n      return;\n    }\n\n    const app = await queryInspectorAppAsync(origin, appId);\n    if (!app) {\n      res.writeHead(404).end('Unable to find inspector target from @react-native/dev-middleware');\n      console.warn(\n        chalk.yellow(\n          'No compatible apps connected. JavaScript Debugging can only be used with the Hermes engine.'\n        )\n      );\n      return;\n    }\n\n    if (req.method === 'GET') {\n      const data = JSON.stringify(app);\n      res.writeHead(200, {\n        'Content-Type': 'application/json; charset=UTF-8',\n        'Cache-Control': 'no-cache',\n        'Content-Length': data.length.toString(),\n      });\n      res.end(data);\n    } else if (req.method === 'POST' || req.method === 'PUT') {\n      try {\n        await openJsInspector(origin, app);\n      } catch (error: any) {\n        // abort(Error: Command failed: osascript -e POSIX path of (path to application \"google chrome\")\n        // 15:50: execution error: Google Chrome got an error: Application isn’t running. (-600)\n\n        console.error(\n          chalk.red('Error launching JS inspector: ' + (error?.message ?? 'Unknown error occurred'))\n        );\n        res.writeHead(500);\n        res.end();\n        return;\n      }\n      res.end();\n    } else {\n      res.writeHead(405);\n    }\n  };\n}\n\nfunction getServerBase(req: IncomingMessage): string {\n  const scheme =\n    req.socket instanceof TLSSocket && req.socket.encrypted === true ? 'https' : 'http';\n  const { localAddress, localPort } = req.socket;\n  const address = localAddress && net.isIPv6(localAddress) ? `[${localAddress}]` : localAddress;\n  return `${scheme}:${address}:${localPort}`;\n}\n"], "names": ["createJsInspectorMiddleware", "req", "res", "next", "origin", "searchParams", "URL", "url", "getServerBase", "appId", "get", "writeHead", "end", "app", "queryInspectorAppAsync", "console", "warn", "chalk", "yellow", "method", "data", "JSON", "stringify", "length", "toString", "openJsInspector", "error", "red", "message", "scheme", "socket", "TLSSocket", "encrypted", "localAddress", "localPort", "address", "net", "isIPv6"], "mappings": ";;;;+BAagBA;;;eAAAA;;;;gEAbE;;;;;;;gEAGF;;;;;;;yBACU;;;;;;;yBACN;;;;;;6BAEoC;;;;;;AAMjD,SAASA;IACd,OAAO,eAAgBC,GAAoB,EAAEC,GAAmB,EAAEC,IAA2B;QAC3F,MAAM,EAAEC,MAAM,EAAEC,YAAY,EAAE,GAAG,IAAIC,CAAAA,MAAE,KAAC,CAACL,IAAIM,GAAG,IAAI,KAAKC,cAAcP;QACvE,MAAMQ,QAAQJ,aAAaK,GAAG,CAAC,YAAYL,aAAaK,GAAG,CAAC;QAC5D,IAAI,CAACD,OAAO;YACVP,IAAIS,SAAS,CAAC,KAAKC,GAAG,CAAC;YACvB;QACF;QAEA,MAAMC,MAAM,MAAMC,IAAAA,mCAAsB,EAACV,QAAQK;QACjD,IAAI,CAACI,KAAK;YACRX,IAAIS,SAAS,CAAC,KAAKC,GAAG,CAAC;YACvBG,QAAQC,IAAI,CACVC,gBAAK,CAACC,MAAM,CACV;YAGJ;QACF;QAEA,IAAIjB,IAAIkB,MAAM,KAAK,OAAO;YACxB,MAAMC,OAAOC,KAAKC,SAAS,CAACT;YAC5BX,IAAIS,SAAS,CAAC,KAAK;gBACjB,gBAAgB;gBAChB,iBAAiB;gBACjB,kBAAkBS,KAAKG,MAAM,CAACC,QAAQ;YACxC;YACAtB,IAAIU,GAAG,CAACQ;QACV,OAAO,IAAInB,IAAIkB,MAAM,KAAK,UAAUlB,IAAIkB,MAAM,KAAK,OAAO;YACxD,IAAI;gBACF,MAAMM,IAAAA,4BAAe,EAACrB,QAAQS;YAChC,EAAE,OAAOa,OAAY;gBACnB,gGAAgG;gBAChG,wFAAwF;gBAExFX,QAAQW,KAAK,CACXT,gBAAK,CAACU,GAAG,CAAC,mCAAoCD,CAAAA,CAAAA,yBAAAA,MAAOE,OAAO,KAAI,wBAAuB;gBAEzF1B,IAAIS,SAAS,CAAC;gBACdT,IAAIU,GAAG;gBACP;YACF;YACAV,IAAIU,GAAG;QACT,OAAO;YACLV,IAAIS,SAAS,CAAC;QAChB;IACF;AACF;AAEA,SAASH,cAAcP,GAAoB;IACzC,MAAM4B,SACJ5B,IAAI6B,MAAM,YAAYC,gBAAS,IAAI9B,IAAI6B,MAAM,CAACE,SAAS,KAAK,OAAO,UAAU;IAC/E,MAAM,EAAEC,YAAY,EAAEC,SAAS,EAAE,GAAGjC,IAAI6B,MAAM;IAC9C,MAAMK,UAAUF,gBAAgBG,cAAG,CAACC,MAAM,CAACJ,gBAAgB,CAAC,CAAC,EAAEA,aAAa,CAAC,CAAC,GAAGA;IACjF,OAAO,GAAGJ,OAAO,CAAC,EAAEM,QAAQ,CAAC,EAAED,WAAW;AAC5C"}