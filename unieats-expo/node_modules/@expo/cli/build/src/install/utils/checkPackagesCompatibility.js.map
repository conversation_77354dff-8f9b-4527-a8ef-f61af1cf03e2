{"version": 3, "sources": ["../../../../src/install/utils/checkPackagesCompatibility.ts"], "sourcesContent": ["// note(Simek): https://github.com/react-native-community/directory/blob/main/pages/api/libraries/check.ts\nimport chalk from 'chalk';\n\nimport { Log } from '../../log';\nimport { fetch } from '../../utils/fetch';\nimport { learnMore } from '../../utils/link';\n\nexport type ReactNativeDirectoryCheckResult = {\n  unmaintained: boolean;\n  newArchitecture: 'supported' | 'unsupported' | 'untested';\n};\n\nconst ERROR_MESSAGE =\n  'Unable to fetch compatibility data from React Native Directory. Skipping check.';\n\nexport async function checkPackagesCompatibility(packages: string[]) {\n  try {\n    const packagesToCheck = packages.filter(\n      (packageName) =>\n        !packageName.startsWith('@expo/') && !packageName.startsWith('@expo-google-fonts/')\n    );\n\n    if (!packagesToCheck.length) {\n      return;\n    }\n\n    const response = await fetch('https://reactnative.directory/api/libraries/check', {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify({ packages: packagesToCheck }),\n    });\n\n    if (!response.ok) {\n      Log.log(chalk.gray(ERROR_MESSAGE));\n    }\n\n    const packageMetadata = (await response.json()) as Record<\n      string,\n      ReactNativeDirectoryCheckResult\n    >;\n\n    const incompatiblePackages = packagesToCheck.filter(\n      (packageName) => packageMetadata[packageName]?.newArchitecture === 'unsupported'\n    );\n\n    if (incompatiblePackages.length) {\n      Log.warn(\n        chalk.yellow(\n          `${chalk.bold('Warning')}: ${formatPackageNames(incompatiblePackages)} do${incompatiblePackages.length > 1 ? '' : 'es'} not support the New Architecture. ${learnMore('https://docs.expo.dev/guides/new-architecture/')}`\n        )\n      );\n    }\n  } catch {\n    Log.log(chalk.gray(ERROR_MESSAGE));\n  }\n}\n\nfunction formatPackageNames(incompatiblePackages: string[]) {\n  if (incompatiblePackages.length === 1) {\n    return incompatiblePackages.join();\n  }\n\n  const lastPackage = incompatiblePackages.pop();\n  return `${incompatiblePackages.join(', ')} and ${lastPackage}`;\n}\n"], "names": ["checkPackagesCompatibility", "ERROR_MESSAGE", "packages", "packagesToCheck", "filter", "packageName", "startsWith", "length", "response", "fetch", "method", "headers", "body", "JSON", "stringify", "ok", "Log", "log", "chalk", "gray", "packageMetadata", "json", "incompatiblePackages", "newArchitecture", "warn", "yellow", "bold", "formatPackageNames", "learnMore", "join", "lastPackage", "pop"], "mappings": "AAAA,0GAA0G;;;;;+BAepFA;;;eAAAA;;;;gEAdJ;;;;;;qBAEE;uBACE;sBACI;;;;;;AAO1B,MAAMC,gBACJ;AAEK,eAAeD,2BAA2BE,QAAkB;IACjE,IAAI;QACF,MAAMC,kBAAkBD,SAASE,MAAM,CACrC,CAACC,cACC,CAACA,YAAYC,UAAU,CAAC,aAAa,CAACD,YAAYC,UAAU,CAAC;QAGjE,IAAI,CAACH,gBAAgBI,MAAM,EAAE;YAC3B;QACF;QAEA,MAAMC,WAAW,MAAMC,IAAAA,YAAK,EAAC,qDAAqD;YAChFC,QAAQ;YACRC,SAAS;gBACP,gBAAgB;YAClB;YACAC,MAAMC,KAAKC,SAAS,CAAC;gBAAEZ,UAAUC;YAAgB;QACnD;QAEA,IAAI,CAACK,SAASO,EAAE,EAAE;YAChBC,QAAG,CAACC,GAAG,CAACC,gBAAK,CAACC,IAAI,CAAClB;QACrB;QAEA,MAAMmB,kBAAmB,MAAMZ,SAASa,IAAI;QAK5C,MAAMC,uBAAuBnB,gBAAgBC,MAAM,CACjD,CAACC;gBAAgBe;mBAAAA,EAAAA,+BAAAA,eAAe,CAACf,YAAY,qBAA5Be,6BAA8BG,eAAe,MAAK;;QAGrE,IAAID,qBAAqBf,MAAM,EAAE;YAC/BS,QAAG,CAACQ,IAAI,CACNN,gBAAK,CAACO,MAAM,CACV,GAAGP,gBAAK,CAACQ,IAAI,CAAC,WAAW,EAAE,EAAEC,mBAAmBL,sBAAsB,GAAG,EAAEA,qBAAqBf,MAAM,GAAG,IAAI,KAAK,KAAK,mCAAmC,EAAEqB,IAAAA,eAAS,EAAC,mDAAmD;QAG/N;IACF,EAAE,OAAM;QACNZ,QAAG,CAACC,GAAG,CAACC,gBAAK,CAACC,IAAI,CAAClB;IACrB;AACF;AAEA,SAAS0B,mBAAmBL,oBAA8B;IACxD,IAAIA,qBAAqBf,MAAM,KAAK,GAAG;QACrC,OAAOe,qBAAqBO,IAAI;IAClC;IAEA,MAAMC,cAAcR,qBAAqBS,GAAG;IAC5C,OAAO,GAAGT,qBAAqBO,IAAI,CAAC,MAAM,KAAK,EAAEC,aAAa;AAChE"}