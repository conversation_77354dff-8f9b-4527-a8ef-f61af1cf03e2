{"version": 3, "sources": ["../../../src/export/metroAssetLocalPath.ts"], "sourcesContent": ["/**\n * Copyright © 2023 650 Industries.\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * Based on the community asset persisting for Metro but with base path and web support:\n * https://github.com/facebook/react-native/blob/d6e0bc714ad4d215ede4949d3c4f44af6dea5dd3/packages/community-cli-plugin/src/commands/bundle/saveAssets.js#L1\n */\nimport type { AssetData } from 'metro';\nimport path from 'path';\n\nexport function getAssetLocalPath(\n  asset: Pick<AssetData, 'type' | 'httpServerLocation' | 'name'>,\n  { baseUrl, scale, platform }: { baseUrl?: string; scale: number; platform: string }\n): string {\n  if (platform === 'android') {\n    return getAssetLocalPathAndroid(asset, { baseUrl, scale });\n  }\n  return getAssetLocalPathDefault(asset, { baseUrl, scale });\n}\n\nfunction getAssetLocalPathAndroid(\n  asset: Pick<AssetData, 'type' | 'httpServerLocation' | 'name'>,\n  {\n    baseUrl,\n    scale,\n  }: {\n    // TODO: baseUrl support\n    baseUrl?: string;\n    scale: number;\n  }\n): string {\n  const androidFolder = getAndroidResourceFolderName(asset, scale);\n  const fileName = getResourceIdentifier(asset);\n  return path.join(androidFolder, `${fileName}.${asset.type}`);\n}\n\nfunction getAssetLocalPathDefault(\n  asset: Pick<AssetData, 'type' | 'httpServerLocation' | 'name'>,\n  { baseUrl, scale }: { baseUrl?: string; scale: number }\n): string {\n  const suffix = scale === 1 ? '' : `@${scale}x`;\n  const fileName = `${asset.name}${suffix}.${asset.type}`;\n\n  const adjustedHttpServerLocation = stripAssetPrefix(asset.httpServerLocation, baseUrl);\n\n  return path.join(\n    // Assets can have relative paths outside of the project root.\n    // Replace `../` with `_` to make sure they don't end up outside of\n    // the expected assets directory.\n    adjustedHttpServerLocation.replace(/^\\/+/g, '').replace(/\\.\\.\\//g, '_'),\n    fileName\n  );\n}\n\nexport function stripAssetPrefix(path: string, baseUrl?: string) {\n  path = path.replace(/\\/assets\\?export_path=(.*)/, '$1');\n\n  // TODO: Windows?\n  if (baseUrl) {\n    return path.replace(/^\\/+/g, '').replace(\n      new RegExp(\n        `^${baseUrl\n          .replace(/^\\/+/g, '')\n          .replace(/[|\\\\{}()[\\]^$+*?.]/g, '\\\\$&')\n          .replace(/-/g, '\\\\x2d')}`,\n        'g'\n      ),\n      ''\n    );\n  }\n  return path;\n}\n\n/**\n * FIXME: using number to represent discrete scale numbers is fragile in essence because of\n * floating point numbers imprecision.\n */\nfunction getAndroidAssetSuffix(scale: number): string | null {\n  switch (scale) {\n    case 0.75:\n      return 'ldpi';\n    case 1:\n      return 'mdpi';\n    case 1.5:\n      return 'hdpi';\n    case 2:\n      return 'xhdpi';\n    case 3:\n      return 'xxhdpi';\n    case 4:\n      return 'xxxhdpi';\n    default:\n      return null;\n  }\n}\n\n// See https://developer.android.com/guide/topics/resources/drawable-resource.html\nexport const drawableFileTypes = new Set<string>(['gif', 'jpeg', 'jpg', 'png', 'webp', 'xml']);\n\nfunction getAndroidResourceFolderName(asset: Pick<AssetData, 'type'>, scale: number): string {\n  if (!drawableFileTypes.has(asset.type)) {\n    return 'raw';\n  }\n  const suffix = getAndroidAssetSuffix(scale);\n  if (!suffix) {\n    throw new Error(\n      `Asset \"${JSON.stringify(asset)}\" does not use a supported Android resolution suffix`\n    );\n  }\n  return `drawable-${suffix}`;\n}\n\nfunction getResourceIdentifier(asset: Pick<AssetData, 'httpServerLocation' | 'name'>): string {\n  const folderPath = getBaseUrl(asset);\n  return `${folderPath}/${asset.name}`\n    .toLowerCase()\n    .replace(/\\//g, '_') // Encode folder structure in file name\n    .replace(/([^a-z0-9_])/g, '') // Remove illegal chars\n    .replace(/^assets_/, ''); // Remove \"assets_\" prefix\n}\n\nfunction getBaseUrl(asset: Pick<AssetData, 'httpServerLocation'>): string {\n  let baseUrl = asset.httpServerLocation;\n  if (baseUrl[0] === '/') {\n    baseUrl = baseUrl.substring(1);\n  }\n  return baseUrl;\n}\n"], "names": ["drawableFileTypes", "getAssetLocalPath", "stripAssetPrefix", "asset", "baseUrl", "scale", "platform", "getAssetLocalPathAndroid", "getAssetLocalPathDefault", "androidFolder", "getAndroidResourceFolderName", "fileName", "getResourceIdentifier", "path", "join", "type", "suffix", "name", "adjustedHttpServerLocation", "httpServerLocation", "replace", "RegExp", "getAndroidAssetSuffix", "Set", "has", "Error", "JSON", "stringify", "folderPath", "getBaseUrl", "toLowerCase", "substring"], "mappings": "AAAA;;;;;;;;;CASC;;;;;;;;;;;IA2FYA,iBAAiB;eAAjBA;;IAvFGC,iBAAiB;eAAjBA;;IA4CAC,gBAAgB;eAAhBA;;;;gEA9CC;;;;;;;;;;;AAEV,SAASD,kBACdE,KAA8D,EAC9D,EAAEC,OAAO,EAAEC,KAAK,EAAEC,QAAQ,EAAyD;IAEnF,IAAIA,aAAa,WAAW;QAC1B,OAAOC,yBAAyBJ,OAAO;YAAEC;YAASC;QAAM;IAC1D;IACA,OAAOG,yBAAyBL,OAAO;QAAEC;QAASC;IAAM;AAC1D;AAEA,SAASE,yBACPJ,KAA8D,EAC9D,EACEC,OAAO,EACPC,KAAK,EAKN;IAED,MAAMI,gBAAgBC,6BAA6BP,OAAOE;IAC1D,MAAMM,WAAWC,sBAAsBT;IACvC,OAAOU,eAAI,CAACC,IAAI,CAACL,eAAe,GAAGE,SAAS,CAAC,EAAER,MAAMY,IAAI,EAAE;AAC7D;AAEA,SAASP,yBACPL,KAA8D,EAC9D,EAAEC,OAAO,EAAEC,KAAK,EAAuC;IAEvD,MAAMW,SAASX,UAAU,IAAI,KAAK,CAAC,CAAC,EAAEA,MAAM,CAAC,CAAC;IAC9C,MAAMM,WAAW,GAAGR,MAAMc,IAAI,GAAGD,OAAO,CAAC,EAAEb,MAAMY,IAAI,EAAE;IAEvD,MAAMG,6BAA6BhB,iBAAiBC,MAAMgB,kBAAkB,EAAEf;IAE9E,OAAOS,eAAI,CAACC,IAAI,CACd,8DAA8D;IAC9D,mEAAmE;IACnE,iCAAiC;IACjCI,2BAA2BE,OAAO,CAAC,SAAS,IAAIA,OAAO,CAAC,WAAW,MACnET;AAEJ;AAEO,SAAST,iBAAiBW,IAAY,EAAET,OAAgB;IAC7DS,OAAOA,KAAKO,OAAO,CAAC,8BAA8B;IAElD,iBAAiB;IACjB,IAAIhB,SAAS;QACX,OAAOS,KAAKO,OAAO,CAAC,SAAS,IAAIA,OAAO,CACtC,IAAIC,OACF,CAAC,CAAC,EAAEjB,QACDgB,OAAO,CAAC,SAAS,IACjBA,OAAO,CAAC,uBAAuB,QAC/BA,OAAO,CAAC,MAAM,UAAU,EAC3B,MAEF;IAEJ;IACA,OAAOP;AACT;AAEA;;;CAGC,GACD,SAASS,sBAAsBjB,KAAa;IAC1C,OAAQA;QACN,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT;YACE,OAAO;IACX;AACF;AAGO,MAAML,oBAAoB,IAAIuB,IAAY;IAAC;IAAO;IAAQ;IAAO;IAAO;IAAQ;CAAM;AAE7F,SAASb,6BAA6BP,KAA8B,EAAEE,KAAa;IACjF,IAAI,CAACL,kBAAkBwB,GAAG,CAACrB,MAAMY,IAAI,GAAG;QACtC,OAAO;IACT;IACA,MAAMC,SAASM,sBAAsBjB;IACrC,IAAI,CAACW,QAAQ;QACX,MAAM,IAAIS,MACR,CAAC,OAAO,EAAEC,KAAKC,SAAS,CAACxB,OAAO,oDAAoD,CAAC;IAEzF;IACA,OAAO,CAAC,SAAS,EAAEa,QAAQ;AAC7B;AAEA,SAASJ,sBAAsBT,KAAqD;IAClF,MAAMyB,aAAaC,WAAW1B;IAC9B,OAAO,GAAGyB,WAAW,CAAC,EAAEzB,MAAMc,IAAI,EAAE,CACjCa,WAAW,GACXV,OAAO,CAAC,OAAO,KAAK,uCAAuC;KAC3DA,OAAO,CAAC,iBAAiB,IAAI,uBAAuB;KACpDA,OAAO,CAAC,YAAY,KAAK,0BAA0B;AACxD;AAEA,SAASS,WAAW1B,KAA4C;IAC9D,IAAIC,UAAUD,MAAMgB,kBAAkB;IACtC,IAAIf,OAAO,CAAC,EAAE,KAAK,KAAK;QACtBA,UAAUA,QAAQ2B,SAAS,CAAC;IAC9B;IACA,OAAO3B;AACT"}