{"version": 3, "sources": ["../../../src/utils/dir.ts"], "sourcesContent": ["import fs from 'fs';\nimport path from 'path';\n\nexport function fileExistsSync(file: string): boolean {\n  return !!fs\n    .statSync(file, {\n      throwIfNoEntry: false,\n    })\n    ?.isFile();\n}\n\nexport function directoryExistsSync(file: string): boolean {\n  return !!fs\n    .statSync(file, {\n      throwIfNoEntry: false,\n    })\n    ?.isDirectory();\n}\n\nexport async function directoryExistsAsync(file: string): Promise<boolean> {\n  return (await fs.promises.stat(file).catch(() => null))?.isDirectory() ?? false;\n}\n\nexport async function fileExistsAsync(file: string): Promise<boolean> {\n  return (await fs.promises.stat(file).catch(() => null))?.isFile() ?? false;\n}\n\nexport const ensureDirectoryAsync = (path: string) => fs.promises.mkdir(path, { recursive: true });\n\nexport const ensureDirectory = (path: string): void => {\n  fs.mkdirSync(path, {\n    recursive: true,\n  });\n};\n\nexport const copySync = (src: string, dest: string): void => {\n  const destParent = path.dirname(dest);\n  if (!fs.existsSync(destParent)) ensureDirectory(destParent);\n  fs.cpSync(src, dest, {\n    recursive: true,\n    force: true,\n  });\n};\n\nexport const copyAsync = async (src: string, dest: string): Promise<void> => {\n  const destParent = path.dirname(dest);\n  if (!fs.existsSync(destParent)) {\n    await fs.promises.mkdir(destParent, { recursive: true });\n  }\n  await fs.promises.cp(src, dest, {\n    recursive: true,\n    force: true,\n  });\n};\n\nexport const removeAsync = (path: string): Promise<void> => {\n  return fs.promises.rm(path, {\n    recursive: true,\n    force: true,\n  });\n};\n"], "names": ["copyAsync", "copySync", "directoryExistsAsync", "directoryExistsSync", "ensureDirectory", "ensureDirectoryAsync", "fileExistsAsync", "fileExistsSync", "removeAsync", "file", "fs", "statSync", "throwIfNoEntry", "isFile", "isDirectory", "promises", "stat", "catch", "path", "mkdir", "recursive", "mkdirSync", "src", "dest", "dest<PERSON>arent", "dirname", "existsSync", "cpSync", "force", "cp", "rm"], "mappings": ";;;;;;;;;;;IA4CaA,SAAS;eAATA;;IATAC,QAAQ;eAARA;;IAhBSC,oBAAoB;eAApBA;;IARNC,mBAAmB;eAAnBA;;IAkBHC,eAAe;eAAfA;;IAFAC,oBAAoB;eAApBA;;IAJSC,eAAe;eAAfA;;IApBNC,cAAc;eAAdA;;IAoDHC,WAAW;eAAXA;;;;gEAvDE;;;;;;;gEACE;;;;;;;;;;;AAEV,SAASD,eAAeE,IAAY;QAChCC;IAAT,OAAO,CAAC,GAACA,eAAAA,aAAE,CACRC,QAAQ,CAACF,MAAM;QACdG,gBAAgB;IAClB,uBAHOF,aAILG,MAAM;AACZ;AAEO,SAASV,oBAAoBM,IAAY;QACrCC;IAAT,OAAO,CAAC,GAACA,eAAAA,aAAE,CACRC,QAAQ,CAACF,MAAM;QACdG,gBAAgB;IAClB,uBAHOF,aAILI,WAAW;AACjB;AAEO,eAAeZ,qBAAqBO,IAAY;QAC7C;IAAR,OAAO,EAAC,QAAA,MAAMC,aAAE,CAACK,QAAQ,CAACC,IAAI,CAACP,MAAMQ,KAAK,CAAC,IAAM,0BAA1C,AAAC,MAAiDH,WAAW,OAAM;AAC5E;AAEO,eAAeR,gBAAgBG,IAAY;QACxC;IAAR,OAAO,EAAC,QAAA,MAAMC,aAAE,CAACK,QAAQ,CAACC,IAAI,CAACP,MAAMQ,KAAK,CAAC,IAAM,0BAA1C,AAAC,MAAiDJ,MAAM,OAAM;AACvE;AAEO,MAAMR,uBAAuB,CAACa,OAAiBR,aAAE,CAACK,QAAQ,CAACI,KAAK,CAACD,MAAM;QAAEE,WAAW;IAAK;AAEzF,MAAMhB,kBAAkB,CAACc;IAC9BR,aAAE,CAACW,SAAS,CAACH,MAAM;QACjBE,WAAW;IACb;AACF;AAEO,MAAMnB,WAAW,CAACqB,KAAaC;IACpC,MAAMC,aAAaN,eAAI,CAACO,OAAO,CAACF;IAChC,IAAI,CAACb,aAAE,CAACgB,UAAU,CAACF,aAAapB,gBAAgBoB;IAChDd,aAAE,CAACiB,MAAM,CAACL,KAAKC,MAAM;QACnBH,WAAW;QACXQ,OAAO;IACT;AACF;AAEO,MAAM5B,YAAY,OAAOsB,KAAaC;IAC3C,MAAMC,aAAaN,eAAI,CAACO,OAAO,CAACF;IAChC,IAAI,CAACb,aAAE,CAACgB,UAAU,CAACF,aAAa;QAC9B,MAAMd,aAAE,CAACK,QAAQ,CAACI,KAAK,CAACK,YAAY;YAAEJ,WAAW;QAAK;IACxD;IACA,MAAMV,aAAE,CAACK,QAAQ,CAACc,EAAE,CAACP,KAAKC,MAAM;QAC9BH,WAAW;QACXQ,OAAO;IACT;AACF;AAEO,MAAMpB,cAAc,CAACU;IAC1B,OAAOR,aAAE,CAACK,QAAQ,CAACe,EAAE,CAACZ,MAAM;QAC1BE,WAAW;QACXQ,OAAO;IACT;AACF"}