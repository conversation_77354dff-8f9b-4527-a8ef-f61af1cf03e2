export declare const StretchInData: {
    StretchInX: {
        name: string;
        style: {
            0: {
                transform: {
                    scaleX: number;
                }[];
            };
            100: {
                transform: {
                    scaleX: number;
                }[];
            };
        };
        duration: number;
    };
    StretchInY: {
        name: string;
        style: {
            0: {
                transform: {
                    scaleY: number;
                }[];
            };
            100: {
                transform: {
                    scaleY: number;
                }[];
            };
        };
        duration: number;
    };
};
export declare const StretchOutData: {
    StretchOutX: {
        name: string;
        style: {
            0: {
                transform: {
                    scaleX: number;
                }[];
            };
            100: {
                transform: {
                    scaleX: number;
                }[];
            };
        };
        duration: number;
    };
    StretchOutY: {
        name: string;
        style: {
            0: {
                transform: {
                    scaleY: number;
                }[];
            };
            100: {
                transform: {
                    scaleY: number;
                }[];
            };
        };
        duration: number;
    };
};
export declare const StretchIn: {
    StretchInX: {
        style: string;
        duration: number;
    };
    StretchInY: {
        style: string;
        duration: number;
    };
};
export declare const StretchOut: {
    StretchOutX: {
        style: string;
        duration: number;
    };
    StretchOutY: {
        style: string;
        duration: number;
    };
};
//# sourceMappingURL=Stretch.web.d.ts.map