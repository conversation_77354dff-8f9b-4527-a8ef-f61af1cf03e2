{"version": 3, "names": ["SwipeRight", "topScreenStyle", "event", "transform", "translateX", "translationX", "belowTopScreenStyle", "screenSize", "width", "SwipeLeft", "SwipeDown", "translateY", "translationY", "height", "SwipeUp", "TwoDimensional", "_screenSize", "_event", "Horizontal", "Vertical", "SwipeRightFade", "opacity", "Math", "abs", "ScreenTransition"], "sourceRoot": "../../../src", "sources": ["screenTransition/presets.ts"], "mappings": "AAAA,YAAY;;AAIZ,MAAMA,UAAoC,GAAG;EAC3CC,cAAc,EAAGC,KAAK,IAAK;IACzB,SAAS;;IACT,OAAO;MACLC,SAAS,EAAE,CAAC;QAAEC,UAAU,EAAEF,KAAK,CAACG;MAAa,CAAC;IAChD,CAAC;EACH,CAAC;EACDC,mBAAmB,EAAEA,CAACJ,KAAK,EAAEK,UAAU,KAAK;IAC1C,SAAS;;IACT,OAAO;MACLJ,SAAS,EAAE,CACT;QAAEC,UAAU,EAAE,CAACF,KAAK,CAACG,YAAY,GAAGE,UAAU,CAACC,KAAK,IAAI;MAAI,CAAC;IAEjE,CAAC;EACH;AACF,CAAC;AAED,MAAMC,SAAmC,GAAG;EAC1CR,cAAc,EAAGC,KAAK,IAAK;IACzB,SAAS;;IACT,OAAO;MACLC,SAAS,EAAE,CAAC;QAAEC,UAAU,EAAEF,KAAK,CAACG;MAAa,CAAC;IAChD,CAAC;EACH,CAAC;EACDC,mBAAmB,EAAEA,CAACJ,KAAK,EAAEK,UAAU,KAAK;IAC1C,SAAS;;IACT,OAAO;MACLJ,SAAS,EAAE,CACT;QAAEC,UAAU,EAAE,CAACF,KAAK,CAACG,YAAY,GAAGE,UAAU,CAACC,KAAK,IAAI;MAAI,CAAC;IAEjE,CAAC;EACH;AACF,CAAC;AAED,MAAME,SAAmC,GAAG;EAC1CT,cAAc,EAAGC,KAAK,IAAK;IACzB,SAAS;;IACT,OAAO;MACLC,SAAS,EAAE,CAAC;QAAEQ,UAAU,EAAET,KAAK,CAACU;MAAa,CAAC;IAChD,CAAC;EACH,CAAC;EACDN,mBAAmB,EAAEA,CAACJ,KAAK,EAAEK,UAAU,KAAK;IAC1C,SAAS;;IACT,OAAO;MACLJ,SAAS,EAAE,CACT;QAAEQ,UAAU,EAAE,CAACT,KAAK,CAACU,YAAY,GAAGL,UAAU,CAACM,MAAM,IAAI;MAAI,CAAC;IAElE,CAAC;EACH;AACF,CAAC;AAED,MAAMC,OAAiC,GAAG;EACxCb,cAAc,EAAGC,KAAK,IAAK;IACzB,SAAS;;IACT,OAAO;MACLC,SAAS,EAAE,CAAC;QAAEQ,UAAU,EAAET,KAAK,CAACU;MAAa,CAAC;IAChD,CAAC;EACH,CAAC;EACDN,mBAAmB,EAAEA,CAACJ,KAAK,EAAEK,UAAU,KAAK;IAC1C,SAAS;;IACT,OAAO;MACLJ,SAAS,EAAE,CACT;QAAEQ,UAAU,EAAE,CAACT,KAAK,CAACU,YAAY,GAAGL,UAAU,CAACM,MAAM,IAAI;MAAI,CAAC;IAElE,CAAC;EACH;AACF,CAAC;AAED,MAAME,cAAwC,GAAG;EAC/Cd,cAAc,EAAEA,CAACC,KAAK,EAAEc,WAAW,KAAK;IACtC,SAAS;;IACT,OAAO;MACLb,SAAS,EAAE,CACT;QAAEC,UAAU,EAAEF,KAAK,CAACG;MAAa,CAAC,EAClC;QAAEM,UAAU,EAAET,KAAK,CAACU;MAAa,CAAC;IAEtC,CAAC;EACH,CAAC;EACDN,mBAAmB,EAAEA,CAACW,MAAM,EAAED,WAAW,KAAK;IAC5C,SAAS;;IACT,OAAO,CAAC,CAAC;EACX;AACF,CAAC;AAED,MAAME,UAAoC,GAAG;EAC3CjB,cAAc,EAAEA,CAACC,KAAK,EAAEc,WAAW,KAAK;IACtC,SAAS;;IACT,OAAO;MACLb,SAAS,EAAE,CAAC;QAAEC,UAAU,EAAEF,KAAK,CAACG;MAAa,CAAC;IAChD,CAAC;EACH,CAAC;EACDC,mBAAmB,EAAEA,CAACW,MAAM,EAAED,WAAW,KAAK;IAC5C,SAAS;;IACT,OAAO,CAAC,CAAC;EACX;AACF,CAAC;AAED,MAAMG,QAAkC,GAAG;EACzClB,cAAc,EAAEA,CAACC,KAAK,EAAEc,WAAW,KAAK;IACtC,SAAS;;IACT,OAAO;MACLb,SAAS,EAAE,CAAC;QAAEQ,UAAU,EAAET,KAAK,CAACU;MAAa,CAAC;IAChD,CAAC;EACH,CAAC;EACDN,mBAAmB,EAAEA,CAACW,MAAM,EAAED,WAAW,KAAK;IAC5C,SAAS;;IACT,OAAO,CAAC,CAAC;EACX;AACF,CAAC;AAED,MAAMI,cAAwC,GAAG;EAC/CnB,cAAc,EAAEA,CAACC,KAAK,EAAEK,UAAU,KAAK;IACrC,SAAS;;IACT,OAAO;MACLc,OAAO,EAAE,CAAC,GAAGC,IAAI,CAACC,GAAG,CAACrB,KAAK,CAACG,YAAY,GAAGE,UAAU,CAACC,KAAK;IAC7D,CAAC;EACH,CAAC;EACDF,mBAAmB,EAAEA,CAACW,MAAM,EAAED,WAAW,KAAK;IAC5C,SAAS;;IACT,OAAO,CAAC,CAAC;EACX;AACF,CAAC;AAED,OAAO,MAAMQ,gBAAgB,GAAG;EAC9BxB,UAAU;EACVS,SAAS;EACTC,SAAS;EACTI,OAAO;EACPI,UAAU;EACVC,QAAQ;EACRJ,cAAc;EACdK;AACF,CAAC", "ignoreList": []}