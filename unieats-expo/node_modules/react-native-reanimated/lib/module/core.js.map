{"version": 3, "names": ["controlEdgeToEdgeValues", "isEdgeToEdge", "ReanimatedError", "initializeUIRuntime", "isF<PERSON><PERSON>", "shouldBeUseWeb", "ReanimatedModule", "SensorContainer", "makeShareableCloneRecursive", "startMapper", "stopMapper", "makeMutable", "createWorkletRuntime", "runOnRuntime", "makeShareable", "executeOnUIRuntimeSync", "runOnJS", "runOnUI", "EDGE_TO_EDGE", "SHOULD_BE_USE_WEB", "isReanimated3", "isConfigured", "getViewProp", "viewTag", "propName", "component", "Promise", "resolve", "reject", "result", "substr", "getSensorContainer", "global", "__sensorContainer", "registerEventHandler", "<PERSON><PERSON><PERSON><PERSON>", "eventName", "emitterReactTag", "handleAndFlushAnimationFrame", "eventTimestamp", "event", "__frameTimestamp", "__flushAnimationFrame", "undefined", "unregisterEventHandler", "id", "subscribeForKeyboardEvents", "options", "state", "height", "now", "_getAnimationTimestamp", "__DEV__", "isStatusBarTranslucentAndroid", "isNavigationBarTranslucentAndroid", "unsubscribeFromKeyboardEvents", "listenerId", "registerSensor", "sensorType", "config", "sensorContainer", "initializeSensor", "unregisterSensor", "sensorId", "featuresConfig", "enableLayoutAnimations", "setByUser", "flag", "isCallByUser", "configureLayoutAnimationBatch", "layoutAnimationsBatch", "setShouldAnimateExitingForTag", "shouldAnimate", "jsiConfigureProps", "uiProps", "nativeProps", "configureProps", "markNodeAsRemovable", "shadowNodeWrapper", "unmarkNodeAsRemovable"], "sourceRoot": "../../src", "sources": ["core.ts"], "mappings": "AAAA,YAAY;;AACZ,SACEA,uBAAuB,EACvBC,YAAY,QACP,8BAA8B;AAarC,SAASC,eAAe,QAAQ,aAAU;AAC1C,SAASC,mBAAmB,QAAQ,mBAAgB;AACpD,SAASC,QAAQ,EAAEC,cAAc,QAAQ,sBAAmB;AAC5D,SAASC,gBAAgB,QAAQ,oBAAoB;AACrD,SAASC,eAAe,QAAQ,sBAAmB;AACnD,SAASC,2BAA2B,QAAQ,iBAAc;AAE1D,SAASC,WAAW,EAAEC,UAAU,QAAQ,cAAW;AACnD,SAASC,WAAW,QAAQ,eAAY;AAExC,SAASC,oBAAoB,EAAEC,YAAY,QAAQ,eAAY;AAC/D,SAASC,aAAa,EAAEN,2BAA2B,QAAQ,iBAAc;AACzE,SAASO,sBAAsB,EAAEC,OAAO,EAAEC,OAAO,QAAQ,cAAW;AAEpE,MAAMC,YAAY,GAAGjB,YAAY,CAAC,CAAC;AACnC,MAAMkB,iBAAiB,GAAGd,cAAc,CAAC,CAAC;;AAE1C;AACA,OAAO,MAAMe,aAAa,GAAGA,CAAA,KAAM,IAAI;;AAEvC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,YAAY,GAAGD,aAAa;AAEzC,OAAO,SAASE,WAAWA,CACzBC,OAAe,EACfC,QAAgB,EAChBC,SAA2B,EACf;EACZ,IAAIrB,QAAQ,CAAC,CAAC,IAAI,CAACqB,SAAS,EAAE;IAC5B,MAAM,IAAIvB,eAAe,CACvB,oFACF,CAAC;EACH;;EAEA;EACA,OAAO,IAAIwB,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;IACtC,OAAOtB,gBAAgB,CAACgB,WAAW,CACjCC,OAAO,EACPC,QAAQ,EACRC,SAAS,EACRI,MAAS,IAAK;MACb,IAAI,OAAOA,MAAM,KAAK,QAAQ,IAAIA,MAAM,CAACC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,QAAQ,EAAE;QAClEF,MAAM,CAACC,MAAM,CAAC;MAChB,CAAC,MAAM;QACLF,OAAO,CAACE,MAAM,CAAC;MACjB;IACF,CACF,CAAC;EACH,CAAC,CAAC;AACJ;AAEA,SAASE,kBAAkBA,CAAA,EAAoB;EAC7C,IAAI,CAACC,MAAM,CAACC,iBAAiB,EAAE;IAC7BD,MAAM,CAACC,iBAAiB,GAAG,IAAI1B,eAAe,CAAC,CAAC;EAClD;EACA,OAAOyB,MAAM,CAACC,iBAAiB;AACjC;AAEA,OAAO,SAASC,oBAAoBA,CAClCC,YAAgC,EAChCC,SAAiB,EACjBC,eAAe,GAAG,CAAC,CAAC,EACZ;EACR,SAASC,4BAA4BA,CAACC,cAAsB,EAAEC,KAAQ,EAAE;IACtE,SAAS;;IACTR,MAAM,CAACS,gBAAgB,GAAGF,cAAc;IACxCJ,YAAY,CAACK,KAAK,CAAC;IACnBR,MAAM,CAACU,qBAAqB,CAACH,cAAc,CAAC;IAC5CP,MAAM,CAACS,gBAAgB,GAAGE,SAAS;EACrC;EACA,OAAOrC,gBAAgB,CAAC4B,oBAAoB,CAC1C1B,2BAA2B,CACzB8B,4BACF,CAAC,EACDF,SAAS,EACTC,eACF,CAAC;AACH;AAEA,OAAO,SAASO,sBAAsBA,CAACC,EAAU,EAAQ;EACvD,OAAOvC,gBAAgB,CAACsC,sBAAsB,CAACC,EAAE,CAAC;AACpD;AAEA,OAAO,SAASC,0BAA0BA,CACxCX,YAAqD,EACrDY,OAAgC,EACxB;EACR;EACA;EACA,SAAST,4BAA4BA,CAACU,KAAa,EAAEC,MAAc,EAAE;IACnE,SAAS;;IACT,MAAMC,GAAG,GAAGlB,MAAM,CAACmB,sBAAsB,CAAC,CAAC;IAC3CnB,MAAM,CAACS,gBAAgB,GAAGS,GAAG;IAC7Bf,YAAY,CAACa,KAAK,EAAEC,MAAM,CAAC;IAC3BjB,MAAM,CAACU,qBAAqB,CAACQ,GAAG,CAAC;IACjClB,MAAM,CAACS,gBAAgB,GAAGE,SAAS;EACrC;EAEA,IAAIS,OAAO,EAAE;IACXpD,uBAAuB,CAAC;MACtBqD,6BAA6B,EAAEN,OAAO,CAACM,6BAA6B;MACpEC,iCAAiC,EAC/BP,OAAO,CAACO;IACZ,CAAC,CAAC;EACJ;EAEA,OAAOhD,gBAAgB,CAACwC,0BAA0B,CAChDtC,2BAA2B,CACzB8B,4BACF,CAAC,EACDpB,YAAY,KAAK6B,OAAO,CAACM,6BAA6B,IAAI,KAAK,CAAC,EAChEnC,YAAY,KAAK6B,OAAO,CAACO,iCAAiC,IAAI,KAAK,CACrE,CAAC;AACH;AAEA,OAAO,SAASC,6BAA6BA,CAACC,UAAkB,EAAQ;EACtE,OAAOlD,gBAAgB,CAACiD,6BAA6B,CAACC,UAAU,CAAC;AACnE;AAEA,OAAO,SAASC,cAAcA,CAC5BC,UAAsB,EACtBC,MAAoB,EACpBxB,YAGS,EACD;EACR,MAAMyB,eAAe,GAAG7B,kBAAkB,CAAC,CAAC;EAC5C,OAAO6B,eAAe,CAACH,cAAc,CACnCC,UAAU,EACVC,MAAM,EACNnD,2BAA2B,CAAC2B,YAA+B,CAC7D,CAAC;AACH;AAEA,OAAO,SAAS0B,gBAAgBA,CAC9BH,UAAsB,EACtBC,MAAoB,EACkB;EACtC,MAAMC,eAAe,GAAG7B,kBAAkB,CAAC,CAAC;EAC5C,OAAO6B,eAAe,CAACC,gBAAgB,CAACH,UAAU,EAAEC,MAAM,CAAC;AAC7D;AAEA,OAAO,SAASG,gBAAgBA,CAACC,QAAgB,EAAQ;EACvD,MAAMH,eAAe,GAAG7B,kBAAkB,CAAC,CAAC;EAC5C,OAAO6B,eAAe,CAACE,gBAAgB,CAACC,QAAQ,CAAC;AACnD;AAEA5D,mBAAmB,CAACG,gBAAgB,CAAC;AAOrC,IAAI0D,cAA8B,GAAG;EACnCC,sBAAsB,EAAE,KAAK;EAC7BC,SAAS,EAAE;AACb,CAAC;AAED,OAAO,SAASD,sBAAsBA,CACpCE,IAAa,EACbC,YAAY,GAAG,IAAI,EACb;EACN,IAAIA,YAAY,EAAE;IAChBJ,cAAc,GAAG;MACfC,sBAAsB,EAAEE,IAAI;MAC5BD,SAAS,EAAE;IACb,CAAC;IACD5D,gBAAgB,CAAC2D,sBAAsB,CAACE,IAAI,CAAC;EAC/C,CAAC,MAAM,IACL,CAACH,cAAc,CAACE,SAAS,IACzBF,cAAc,CAACC,sBAAsB,KAAKE,IAAI,EAC9C;IACAH,cAAc,CAACC,sBAAsB,GAAGE,IAAI;IAC5C7D,gBAAgB,CAAC2D,sBAAsB,CAACE,IAAI,CAAC;EAC/C;AACF;AAEA,OAAO,SAASE,6BAA6BA,CAC3CC,qBAAiD,EAC3C;EACNhE,gBAAgB,CAAC+D,6BAA6B,CAACC,qBAAqB,CAAC;AACvE;AAEA,OAAO,SAASC,6BAA6BA,CAC3ChD,OAA6B,EAC7BiD,aAAsB,EACtB;EACAlE,gBAAgB,CAACiE,6BAA6B,CAC5ChD,OAAO,EACPiD,aACF,CAAC;AACH;AAEA,OAAO,SAASC,iBAAiBA,CAC/BC,OAAiB,EACjBC,WAAqB,EACf;EACN,IAAI,CAACxD,iBAAiB,EAAE;IACtBb,gBAAgB,CAACsE,cAAc,CAACF,OAAO,EAAEC,WAAW,CAAC;EACvD;AACF;AAEA,OAAO,SAASE,mBAAmBA,CAACC,iBAAoC,EAAE;EACxExE,gBAAgB,CAACuE,mBAAmB,CAACC,iBAAiB,CAAC;AACzD;AAEA,OAAO,SAASC,qBAAqBA,CAACxD,OAAe,EAAE;EACrDjB,gBAAgB,CAACyE,qBAAqB,CAACxD,OAAO,CAAC;AACjD", "ignoreList": []}