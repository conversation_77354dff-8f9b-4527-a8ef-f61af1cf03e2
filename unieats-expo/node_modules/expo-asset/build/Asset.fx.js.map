{"version": 3, "file": "Asset.fx.js", "sourceRoot": "", "sources": ["../src/Asset.fx.ts"], "names": [], "mappings": "AAEA,OAAO,EAAE,KAAK,EAAE,kCAAkC,EAAE,MAAM,SAAS,CAAC;AACpE,OAAO,EAAE,wBAAwB,EAAE,MAAM,iBAAiB,CAAC;AAC3D,OAAO,kBAAkB,EAAE,EAAE,0BAA0B,EAAE,MAAM,sBAAsB,CAAC;AAEtF,+FAA+F;AAC/F,IAAI,wBAAwB,EAAE,CAAC;IAC7B,MAAM,cAAc,GAClB,kBAAkB,CAAC,0BAA0B,IAAI,0BAA0B,CAAC;IAC9E,cAAc,CAAC,CAAC,QAAQ,EAAE,EAAE;QAC1B,IAAI,CAAC;YACH,2FAA2F;YAC3F,IAAI,YAAY,IAAI,QAAQ,CAAC,KAAK,IAAI,QAAQ,CAAC,KAAK,CAAC,UAAU,EAAE,CAAC;gBAChE,MAAM,KAAK,GAAG,KAAK,CAAC,YAAY,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;gBACjD,IAAI,KAAK,CAAC,GAAG,CAAC,UAAU,CAAC,kCAAkC,CAAC,EAAE,CAAC;oBAC7D,6GAA6G;oBAC7G,mHAAmH;oBACnH,oIAAoI;oBACpI,OACE,QACD,CAAC,8BAA8B,EAAS,CAAC;gBAC5C,CAAC;gBACD,OAAO,QAAQ,CAAC,UAAU,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,KAAK,CAAC,QAAS,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;YAC7E,CAAC;iBAAM,CAAC;gBACN,OAAO,QAAQ,CAAC,YAAY,EAAE,CAAC;YACjC,CAAC;QACH,CAAC;QAAC,MAAM,CAAC;YACP,OAAO,QAAQ,CAAC,YAAY,EAAE,CAAC;QACjC,CAAC;IACH,CAAC,CAAC,CAAC;AACL,CAAC", "sourcesContent": ["import type { default as AssetSourceResolver } from 'react-native/Libraries/Image/AssetSourceResolver';\n\nimport { Asset, ANDROID_EMBEDDED_URL_BASE_RESOURCE } from './Asset';\nimport { IS_ENV_WITH_LOCAL_ASSETS } from './PlatformUtils';\nimport resolveAssetSource, { setCustomSourceTransformer } from './resolveAssetSource';\n\n// Override React Native's asset resolution for `Image` components in contexts where it matters\nif (IS_ENV_WITH_LOCAL_ASSETS) {\n  const setTransformer =\n    resolveAssetSource.setCustomSourceTransformer || setCustomSourceTransformer;\n  setTransformer((resolver) => {\n    try {\n      // Bund<PERSON> is using the hashAssetFiles plugin if and only if the fileHashes property exists\n      if ('fileHashes' in resolver.asset && resolver.asset.fileHashes) {\n        const asset = Asset.fromMetadata(resolver.asset);\n        if (asset.uri.startsWith(ANDROID_EMBEDDED_URL_BASE_RESOURCE)) {\n          // TODO(@kitten): See https://github.com/expo/expo/commit/ec940b57a87d99ab4f1d06d87126e662c3f04f04#r155340943\n          // It's unclear whether this is sound since this may be our own AssetSourceResolver, which doesn't have this method\n          // Please compare `AssetSourceResolver` type from `react-native/Libraries/Image/AssetSourceResolver` against `./AssetSourceResolver`\n          return (\n            resolver as unknown as AssetSourceResolver\n          ).resourceIdentifierWithoutScale() as any;\n        }\n        return resolver.fromSource(asset.downloaded ? asset.localUri! : asset.uri);\n      } else {\n        return resolver.defaultAsset();\n      }\n    } catch {\n      return resolver.defaultAsset();\n    }\n  });\n}\n"]}