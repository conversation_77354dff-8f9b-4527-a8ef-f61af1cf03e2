/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 * @flow strict-local
 * @format
 * @oncall react_native
 */

import type { Command } from "@react-native-community/cli-types";

export type { StartCommandArgs } from "./runServer";

declare const startCommand: Command;

declare export default typeof startCommand;
