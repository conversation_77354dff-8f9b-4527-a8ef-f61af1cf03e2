{"version": 3, "names": ["getInvertedMultiplier", "getDistanceForDirection", "layout", "gestureDirection", "multiplier", "height", "width"], "sourceRoot": "../../../src", "sources": ["utils/getDistanceForDirection.tsx"], "mappings": "AACA,OAAOA,qBAAqB,MAAM,yBAAyB;AAE3D,eAAe,SAASC,uBAAuB,CAC7CC,MAAc,EACdC,gBAAkC,EAC1B;EACR,MAAMC,UAAU,GAAGJ,qBAAqB,CAACG,gBAAgB,CAAC;EAE1D,QAAQA,gBAAgB;IACtB,KAAK,UAAU;IACf,KAAK,mBAAmB;MACtB,OAAOD,MAAM,CAACG,MAAM,GAAGD,UAAU;IACnC,KAAK,YAAY;IACjB,KAAK,qBAAqB;MACxB,OAAOF,MAAM,CAACI,KAAK,GAAGF,UAAU;EAAC;AAEvC"}