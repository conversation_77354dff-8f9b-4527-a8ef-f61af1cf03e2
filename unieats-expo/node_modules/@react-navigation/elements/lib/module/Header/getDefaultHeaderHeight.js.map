{"version": 3, "names": ["Platform", "getDefaultHeaderHeight", "layout", "modalPresentation", "statusBarHeight", "headerHeight", "isLandscape", "width", "height", "OS", "isPad", "isTV"], "sourceRoot": "../../../src", "sources": ["Header/getDefaultHeaderHeight.tsx"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,cAAc;AAIvC,eAAe,SAASC,sBAAsB,CAC5CC,MAAc,EACdC,iBAA0B,EAC1BC,eAAuB,EACf;EACR,IAAIC,YAAY;EAEhB,MAAMC,WAAW,GAAGJ,MAAM,CAACK,KAAK,GAAGL,MAAM,CAACM,MAAM;EAEhD,IAAIR,QAAQ,CAACS,EAAE,KAAK,KAAK,EAAE;IACzB,IAAIT,QAAQ,CAACU,KAAK,IAAIV,QAAQ,CAACW,IAAI,EAAE;MACnC,IAAIR,iBAAiB,EAAE;QACrBE,YAAY,GAAG,EAAE;MACnB,CAAC,MAAM;QACLA,YAAY,GAAG,EAAE;MACnB;IACF,CAAC,MAAM;MACL,IAAIC,WAAW,EAAE;QACfD,YAAY,GAAG,EAAE;MACnB,CAAC,MAAM;QACL,IAAIF,iBAAiB,EAAE;UACrBE,YAAY,GAAG,EAAE;QACnB,CAAC,MAAM;UACLA,YAAY,GAAG,EAAE;QACnB;MACF;IACF;EACF,CAAC,MAAM,IAAIL,QAAQ,CAACS,EAAE,KAAK,SAAS,EAAE;IACpCJ,YAAY,GAAG,EAAE;EACnB,CAAC,MAAM;IACLA,YAAY,GAAG,EAAE;EACnB;EAEA,OAAOA,YAAY,GAAGD,eAAe;AACvC"}