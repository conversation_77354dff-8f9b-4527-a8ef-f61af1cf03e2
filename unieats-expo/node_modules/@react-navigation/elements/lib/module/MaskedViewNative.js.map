{"version": 3, "names": ["React", "UIManager", "RNCMaskedView", "require", "default", "e", "isMaskedViewAvailable", "getViewManagerConfig", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "children", "rest"], "sourceRoot": "../../src", "sources": ["MaskedViewNative.tsx"], "mappings": "AAAA;AACA;AACA;AACA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,SAAS,QAAQ,cAAc;AASxC,IAAIC,aAAyC;AAE7C,IAAI;EACF;EACA;EACAA,aAAa,GAAGC,OAAO,CAAC,uCAAuC,CAAC,CAACC,OAAO;AAC1E,CAAC,CAAC,OAAOC,CAAC,EAAE;EACV;AAAA;AAGF,MAAMC,qBAAqB,GACzBL,SAAS,CAACM,oBAAoB,CAAC,eAAe,CAAC,IAAI,IAAI;AAEzD,eAAe,SAASC,UAAU,OAA+B;EAAA,IAA9B;IAAEC,QAAQ;IAAE,GAAGC;EAAY,CAAC;EAC7D,IAAIJ,qBAAqB,IAAIJ,aAAa,EAAE;IAC1C,oBAAO,oBAAC,aAAa,EAAKQ,IAAI,EAAGD,QAAQ,CAAiB;EAC5D;EAEA,OAAOA,QAAQ;AACjB"}