{"version": 3, "names": ["useDescriptors", "state", "screens", "navigation", "screenOptions", "defaultScreenOptions", "onAction", "getState", "setState", "addListener", "addKeyedListener", "onRouteFocus", "router", "emitter", "options", "setOptions", "React", "useState", "onDispatchAction", "onOptionsChange", "stackRef", "useContext", "NavigationBuilderContext", "context", "useMemo", "navigations", "useNavigationCache", "routes", "useRouteCache", "reduce", "acc", "route", "i", "config", "name", "screen", "props", "key", "optionsList", "filter", "Boolean", "customOptions", "curr", "Object", "assign", "mergedOptions", "clearOptions", "o", "_", "rest", "render"], "sourceRoot": "../../src", "sources": ["useDescriptors.tsx"], "mappings": ";;;;;;AAMA;AAEA;AAIA;AACA;AACA;AAUA;AACA;AAA4C;AAAA;AAAA;AAgD5C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACe,SAASA,cAAc,OAmBM;EAAA,IAd1C;IACAC,KAAK;IACLC,OAAO;IACPC,UAAU;IACVC,aAAa;IACbC,oBAAoB;IACpBC,QAAQ;IACRC,QAAQ;IACRC,QAAQ;IACRC,WAAW;IACXC,gBAAgB;IAChBC,YAAY;IACZC,MAAM;IACNC;EACuC,CAAC;EACxC,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGC,KAAK,CAACC,QAAQ,CAAyB,CAAC,CAAC,CAAC;EACxE,MAAM;IAAEC,gBAAgB;IAAEC,eAAe;IAAEC;EAAS,CAAC,GAAGJ,KAAK,CAACK,UAAU,CACtEC,iCAAwB,CACzB;EAED,MAAMC,OAAO,GAAGP,KAAK,CAACQ,OAAO,CAC3B,OAAO;IACLrB,UAAU;IACVG,QAAQ;IACRG,WAAW;IACXC,gBAAgB;IAChBC,YAAY;IACZO,gBAAgB;IAChBC,eAAe;IACfC;EACF,CAAC,CAAC,EACF,CACEjB,UAAU,EACVG,QAAQ,EACRG,WAAW,EACXC,gBAAgB,EAChBC,YAAY,EACZO,gBAAgB,EAChBC,eAAe,EACfC,QAAQ,CACT,CACF;EAED,MAAMK,WAAW,GAAG,IAAAC,2BAAkB,EAAiC;IACrEzB,KAAK;IACLM,QAAQ;IACRJ,UAAU;IACVY,UAAU;IACVH,MAAM;IACNC;EACF,CAAC,CAAC;EAEF,MAAMc,MAAM,GAAG,IAAAC,sBAAa,EAAC3B,KAAK,CAAC0B,MAAM,CAAC;EAE1C,OAAOA,MAAM,CAACE,MAAM,CAiBlB,CAACC,GAAG,EAAEC,KAAK,EAAEC,CAAC,KAAK;IACnB,MAAMC,MAAM,GAAG/B,OAAO,CAAC6B,KAAK,CAACG,IAAI,CAAC;IAClC,MAAMC,MAAM,GAAGF,MAAM,CAACG,KAAK;IAC3B,MAAMjC,UAAU,GAAGsB,WAAW,CAACM,KAAK,CAACM,GAAG,CAAC;IAEzC,MAAMC,WAAW,GAAG;IAClB;IACAlC,aAAa;IACb;IACA,IAAK6B,MAAM,CAACnB,OAAO,GACfmB,MAAM,CAACnB,OAAO,CAACyB,MAAM,CAACC,OAAO,CAAC,GAC9B,EAAE,CAA8C;IACpD;IACAL,MAAM,CAACrB,OAAO;IACd;IACAA,OAAO,CAACiB,KAAK,CAACM,GAAG,CAAC,CACnB;IAED,MAAMI,aAAa,GAAGH,WAAW,CAACT,MAAM,CACtC,CAACC,GAAG,EAAEY,IAAI,KACRC,MAAM,CAACC,MAAM,CACXd,GAAG;IACH;IACA,OAAOY,IAAI,KAAK,UAAU,GAAGA,IAAI,GAAGA,IAAI,CAAC;MAAEX,KAAK;MAAE5B;IAAW,CAAC,CAAC,CAChE,EACH,CAAC,CAAC,CACH;IAED,MAAM0C,aAAa,GAAG;MACpB,IAAI,OAAOxC,oBAAoB,KAAK,UAAU;MAC1C;MACAA,oBAAoB,CAAC;QACnB0B,KAAK;QACL5B,UAAU;QACVW,OAAO,EAAE2B;MACX,CAAC,CAAC,GACFpC,oBAAoB,CAAC;MACzB,GAAGoC;IACL,CAAC;IAED,MAAMK,YAAY,GAAG,MACnB/B,UAAU,CAAEgC,CAAC,IAAK;MAChB,IAAIhB,KAAK,CAACM,GAAG,IAAIU,CAAC,EAAE;QAClB;QACA,MAAM;UAAE,CAAChB,KAAK,CAACM,GAAG,GAAGW,CAAC;UAAE,GAAGC;QAAK,CAAC,GAAGF,CAAC;QACrC,OAAOE,IAAI;MACb;MAEA,OAAOF,CAAC;IACV,CAAC,CAAC;IAEJjB,GAAG,CAACC,KAAK,CAACM,GAAG,CAAC,GAAG;MACfN,KAAK;MACL;MACA5B,UAAU;MACV+C,MAAM,GAAG;QACP,oBACE,oBAAC,iCAAwB,CAAC,QAAQ;UAAC,GAAG,EAAEnB,KAAK,CAACM,GAAI;UAAC,KAAK,EAAEd;QAAQ,gBAChE,oBAAC,0BAAiB,CAAC,QAAQ;UAAC,KAAK,EAAEpB;QAAW,gBAC5C,oBAAC,+BAAsB,CAAC,QAAQ;UAAC,KAAK,EAAE4B;QAAM,gBAC5C,oBAAC,kBAAS;UACR,UAAU,EAAE5B,UAAW;UACvB,KAAK,EAAE4B,KAAM;UACb,MAAM,EAAEI,MAAO;UACf,UAAU,EAAElC,KAAK,CAAC0B,MAAM,CAACK,CAAC,CAAC,CAAC/B,KAAM;UAClC,QAAQ,EAAEM,QAAS;UACnB,QAAQ,EAAEC,QAAS;UACnB,OAAO,EAAEqC,aAAc;UACvB,YAAY,EAAEC;QAAa,EAC3B,CAC8B,CACP,CACK;MAExC,CAAC;MACDhC,OAAO,EAAE+B;IACX,CAAC;IAED,OAAOf,GAAG;EACZ,CAAC,EAAE,CAAC,CAAC,CAAC;AACR"}