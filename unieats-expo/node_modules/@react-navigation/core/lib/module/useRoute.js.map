{"version": 3, "names": ["React", "NavigationRouteContext", "useRoute", "route", "useContext", "undefined", "Error"], "sourceRoot": "../../src", "sources": ["useRoute.tsx"], "mappings": "AACA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,OAAOC,sBAAsB,MAAM,0BAA0B;AAG7D;AACA;AACA;AACA;AACA;AACA,eAAe,SAASC,QAAQ,GAA0C;EACxE,MAAMC,KAAK,GAAGH,KAAK,CAACI,UAAU,CAACH,sBAAsB,CAAC;EAEtD,IAAIE,KAAK,KAAKE,SAAS,EAAE;IACvB,MAAM,IAAIC,KAAK,CACb,iFAAiF,CAClF;EACH;EAEA,OAAOH,KAAK;AACd"}