{"version": 3, "names": ["useThenable", "create", "promise", "React", "useState", "initialState", "undefined", "then", "result", "state", "setState", "resolved", "useEffect", "cancelled", "resolve"], "sourceRoot": "../../src", "sources": ["useThenable.tsx"], "mappings": ";;;;;;AAAA;AAA+B;AAAA;AAEhB,SAASA,WAAW,CAAIC,MAA4B,EAAE;EACnE,MAAM,CAACC,OAAO,CAAC,GAAGC,KAAK,CAACC,QAAQ,CAACH,MAAM,CAAC;EAExC,IAAII,YAAsC,GAAG,CAAC,KAAK,EAAEC,SAAS,CAAC;;EAE/D;EACAJ,OAAO,CAACK,IAAI,CAAEC,MAAM,IAAK;IACvBH,YAAY,GAAG,CAAC,IAAI,EAAEG,MAAM,CAAC;EAC/B,CAAC,CAAC;EAEF,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGP,KAAK,CAACC,QAAQ,CAACC,YAAY,CAAC;EACtD,MAAM,CAACM,QAAQ,CAAC,GAAGF,KAAK;EAExBN,KAAK,CAACS,SAAS,CAAC,MAAM;IACpB,IAAIC,SAAS,GAAG,KAAK;IAErB,MAAMC,OAAO,GAAG,YAAY;MAC1B,IAAIN,MAAM;MAEV,IAAI;QACFA,MAAM,GAAG,MAAMN,OAAO;MACxB,CAAC,SAAS;QACR,IAAI,CAACW,SAAS,EAAE;UACdH,QAAQ,CAAC,CAAC,IAAI,EAAEF,MAAM,CAAC,CAAC;QAC1B;MACF;IACF,CAAC;IAED,IAAI,CAACG,QAAQ,EAAE;MACbG,OAAO,EAAE;IACX;IAEA,OAAO,MAAM;MACXD,SAAS,GAAG,IAAI;IAClB,CAAC;EACH,CAAC,EAAE,CAACX,OAAO,EAAES,QAAQ,CAAC,CAAC;EAEvB,OAAOF,KAAK;AACd"}