-- Create user_settings table for individual user preferences
CREATE TABLE IF NOT EXISTS user_settings (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID UNIQUE REFERENCES auth.users(id) ON DELETE CASCADE,
  notifications_new_orders B<PERSON><PERSON><PERSON>N DEFAULT true,
  notifications_low_stock B<PERSON><PERSON><PERSON>N DEFAULT true,
  notifications_reviews BOOLEAN DEFAULT true,
  notifications_marketing BOOLEAN DEFAULT false,
  analytics_tracking BOOLEAN DEFAULT true,
  auto_update BOOLEAN DEFAULT false,
  data_retention_days INTEGER DEFAULT 90,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create business_hours table for cafeteria operating hours
CREATE TABLE IF NOT EXISTS business_hours (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  cafeteria_id UUID REFERENCES cafeterias(id) ON DELETE CASCADE,
  day_of_week INTEGER NOT NULL CHECK (day_of_week >= 0 AND day_of_week <= 6), -- 0 = Sunday, 6 = Saturday
  open_time TIME NOT NULL DEFAULT '08:00',
  close_time TIME NOT NULL DEFAULT '20:00',
  is_closed BOOLEAN DEFAULT false,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(cafeteria_id, day_of_week)
);

-- Add missing columns to cafeteria_settings if they don't exist
ALTER TABLE cafeteria_settings ADD COLUMN IF NOT EXISTS online_ordering BOOLEAN DEFAULT true;
ALTER TABLE cafeteria_settings ADD COLUMN IF NOT EXISTS auto_accept_orders BOOLEAN DEFAULT false;
ALTER TABLE cafeteria_settings ADD COLUMN IF NOT EXISTS default_preparation_time INTEGER DEFAULT 15;

-- Enable RLS on new tables
ALTER TABLE user_settings ENABLE ROW LEVEL SECURITY;
ALTER TABLE business_hours ENABLE ROW LEVEL SECURITY;

-- RLS Policies for user_settings
CREATE POLICY "Users can manage their own settings"
  ON user_settings FOR ALL
  USING (auth.uid() = user_id);

-- RLS Policies for business_hours
CREATE POLICY "Cafeteria owners can manage their business hours"
  ON business_hours FOR ALL
  USING (
    EXISTS (
      SELECT 1 FROM cafeterias 
      WHERE cafeterias.id = business_hours.cafeteria_id 
      AND cafeterias.owner_id = auth.uid()
    )
  );

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_user_settings_user_id ON user_settings(user_id);
CREATE INDEX IF NOT EXISTS idx_business_hours_cafeteria_id ON business_hours(cafeteria_id);
CREATE INDEX IF NOT EXISTS idx_business_hours_day ON business_hours(day_of_week);

-- Create function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for updated_at
CREATE TRIGGER update_user_settings_updated_at 
  BEFORE UPDATE ON user_settings 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_business_hours_updated_at 
  BEFORE UPDATE ON business_hours 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Insert default business hours for existing cafeterias
INSERT INTO business_hours (cafeteria_id, day_of_week, open_time, close_time, is_closed)
SELECT 
  c.id,
  generate_series(0, 6) as day_of_week,
  CASE 
    WHEN generate_series(0, 6) IN (0, 6) THEN '10:00'::TIME -- Sunday and Saturday
    ELSE '08:00'::TIME -- Monday to Friday
  END as open_time,
  CASE 
    WHEN generate_series(0, 6) = 0 THEN '16:00'::TIME -- Sunday
    WHEN generate_series(0, 6) = 6 THEN '18:00'::TIME -- Saturday
    ELSE '20:00'::TIME -- Monday to Friday
  END as close_time,
  false as is_closed
FROM cafeterias c
WHERE NOT EXISTS (
  SELECT 1 FROM business_hours bh 
  WHERE bh.cafeteria_id = c.id
);
