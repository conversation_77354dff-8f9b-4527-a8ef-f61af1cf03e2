import { NextRequest, NextResponse } from 'next/server'
import { createSupabaseAdmin } from '@/lib/supabase'

export async function GET(request: NextRequest) {
  try {
    const supabaseAdmin = createSupabaseAdmin()

    // Fetch cafeteria ratings data
    const { data: cafeterias } = await supabaseAdmin
      .from('cafeterias')
      .select('id, name, location, description')

    const { data: cafeteriaRatings } = await supabaseAdmin
      .from('cafeteria_user_ratings')
      .select(`
        id,
        cafeteria_id,
        user_id,
        rating,
        comment,
        created_at,
        profiles(full_name),
        cafeterias(name, location)
      `)
      .order('created_at', { ascending: false })

    const { data: menuItemRatings } = await supabaseAdmin
      .from('menu_item_ratings')
      .select(`
        id,
        menu_item_id,
        user_id,
        rating,
        review_comment,
        created_at,
        profiles(full_name),
        menu_items(name, cafeteria_id, cafeterias(name))
      `)
      .order('created_at', { ascending: false })

    // Create CSV content
    let csvContent = 'Type,Item Name,Cafeteria,Location,User,Rating,Comment,Date\n'

    // Add cafeteria ratings
    cafeteriaRatings?.forEach(rating => {
      const cafeteria = rating.cafeterias
      const user = rating.profiles
      const row = [
        'Cafeteria',
        `"${cafeteria?.name || 'Unknown'}"`,
        `"${cafeteria?.name || 'Unknown'}"`,
        `"${cafeteria?.location || 'Unknown'}"`,
        `"${user?.full_name || 'Anonymous'}"`,
        rating.rating,
        `"${rating.comment || ''}"`,
        new Date(rating.created_at).toLocaleDateString()
      ].join(',')
      csvContent += row + '\n'
    })

    // Add menu item ratings
    menuItemRatings?.forEach(rating => {
      const menuItem = rating.menu_items
      const cafeteria = menuItem?.cafeterias
      const user = rating.profiles
      const row = [
        'Menu Item',
        `"${menuItem?.name || 'Unknown'}"`,
        `"${cafeteria?.name || 'Unknown'}"`,
        `"${cafeteria?.location || 'Unknown'}"`,
        `"${user?.full_name || 'Anonymous'}"`,
        rating.rating,
        `"${rating.review_comment || ''}"`,
        new Date(rating.created_at).toLocaleDateString()
      ].join(',')
      csvContent += row + '\n'
    })

    return new NextResponse(csvContent, {
      headers: {
        'Content-Type': 'text/csv',
        'Content-Disposition': `attachment; filename="cafeteria-ratings-${new Date().toISOString().split('T')[0]}.csv"`
      }
    })

  } catch (error) {
    console.error('Error exporting ratings:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
