import { NextResponse } from 'next/server'

export async function GET() {
  try {
    // Simple test to verify the API is working
    return NextResponse.json({
      success: true,
      message: 'Notification system test endpoint is working',
      timestamp: new Date().toISOString()
    })
  } catch (error) {
    return NextResponse.json(
      { 
        success: false, 
        error: 'Test endpoint failed',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}
