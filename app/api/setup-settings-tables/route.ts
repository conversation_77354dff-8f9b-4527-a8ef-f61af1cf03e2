import { NextRequest, NextResponse } from 'next/server'
import { supabase } from '@/lib/supabase'

export async function POST(request: NextRequest) {
  try {
    // Create user_settings table
    const userSettingsQuery = `
      CREATE TABLE IF NOT EXISTS user_settings (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        user_id UUID UNIQUE REFERENCES auth.users(id) ON DELETE CASCADE,
        notifications_new_orders BOOLEAN DEFAULT true,
        notifications_low_stock BOOLEAN DEFAULT true,
        notifications_reviews BOOLEAN DEFAULT true,
        notifications_marketing BOOLEAN DEFAULT false,
        analytics_tracking BOOLEAN DEFAULT true,
        auto_update B<PERSON><PERSON>EAN DEFAULT false,
        data_retention_days INTEGER DEFAULT 90,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      );
    `

    // Create business_hours table
    const businessHoursQuery = `
      CREATE TABLE IF NOT EXISTS business_hours (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        cafeteria_id UUID REFERENCES cafeterias(id) ON DELETE CASCADE,
        day_of_week INTEGER NOT NULL CHECK (day_of_week >= 0 AND day_of_week <= 6),
        open_time TIME NOT NULL DEFAULT '08:00',
        close_time TIME NOT NULL DEFAULT '20:00',
        is_closed BOOLEAN DEFAULT false,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        UNIQUE(cafeteria_id, day_of_week)
      );
    `

    // Add missing columns to cafeteria_settings
    const alterCafeteriaSettingsQuery = `
      ALTER TABLE cafeteria_settings 
      ADD COLUMN IF NOT EXISTS online_ordering BOOLEAN DEFAULT true,
      ADD COLUMN IF NOT EXISTS auto_accept_orders BOOLEAN DEFAULT false,
      ADD COLUMN IF NOT EXISTS default_preparation_time INTEGER DEFAULT 15;
    `

    // Enable RLS
    const rlsQueries = `
      ALTER TABLE user_settings ENABLE ROW LEVEL SECURITY;
      ALTER TABLE business_hours ENABLE ROW LEVEL SECURITY;

      -- RLS Policies for user_settings
      DROP POLICY IF EXISTS "Users can manage their own settings" ON user_settings;
      CREATE POLICY "Users can manage their own settings"
        ON user_settings FOR ALL
        USING (auth.uid() = user_id);

      -- RLS Policies for business_hours
      DROP POLICY IF EXISTS "Cafeteria owners can manage their business hours" ON business_hours;
      CREATE POLICY "Cafeteria owners can manage their business hours"
        ON business_hours FOR ALL
        USING (
          EXISTS (
            SELECT 1 FROM cafeterias 
            WHERE cafeterias.id = business_hours.cafeteria_id 
            AND cafeterias.owner_id = auth.uid()
          )
        );
    `

    // For now, let's just try to create the tables by inserting test data
    // This will fail if tables don't exist, which tells us they need to be created

    let tablesExist = {
      userSettings: false,
      businessHours: false,
      cafeteriaSettings: false
    }

    // Test user_settings table
    try {
      const { error } = await supabase.from('user_settings').select('id').limit(1)
      if (!error) tablesExist.userSettings = true
    } catch (e) {
      console.log('user_settings table does not exist')
    }

    // Test business_hours table
    try {
      const { error } = await supabase.from('business_hours').select('id').limit(1)
      if (!error) tablesExist.businessHours = true
    } catch (e) {
      console.log('business_hours table does not exist')
    }

    // Test cafeteria_settings table
    try {
      const { error } = await supabase.from('cafeteria_settings').select('id').limit(1)
      if (!error) tablesExist.cafeteriaSettings = true
    } catch (e) {
      console.log('cafeteria_settings table does not exist')
    }

    return NextResponse.json({
      success: true,
      message: 'Settings tables checked successfully',
      tablesExist,
      note: 'Tables need to be created manually in Supabase dashboard or via migration'
    })

  } catch (error) {
    console.error('Error setting up settings tables:', error)
    return NextResponse.json({ 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown error' 
    }, { status: 500 })
  }
}
